"""
Main Training and Evaluation Script for Heterogeneous Multi-Edge Server DNN Inference Optimization
Implements MADDPG training and baseline comparisons
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import torch
import argparse
import os
import json
from datetime import datetime
from typing import Dict, List, Any

from config import config
from environment import EdgeComputingEnvironment
from networks import MADDPG
from baselines import create_baseline_algorithms, evaluate_baseline

class MADDPGTrainer:
    """MADDPG training and evaluation manager"""
    
    def __init__(self, env: EdgeComputingEnvironment, save_dir: str = "results"):
        self.env = env
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # Initialize MADDPG
        self.maddpg = MADDPG(
            num_agents=env.num_devices,
            state_dim=env.state_dim,
            action_dim=env.action_dim
        )
        
        # Training statistics
        self.training_stats = {
            'episode_rewards': [],
            'episode_energies': [],
            'episode_delays': [],
            'episode_accuracies': [],
            'episode_violations': [],
            'avg_battery_levels': []
        }
        
        # Baseline algorithms
        self.baselines = create_baseline_algorithms()
    
    def train(self, num_episodes: int = None, save_frequency: int = 100,
              eval_frequency: int = 50) -> Dict[str, Any]:
        """
        Train MADDPG algorithm
        
        Args:
            num_episodes: Number of training episodes
            save_frequency: Frequency to save models
            eval_frequency: Frequency to evaluate performance
            
        Returns:
            Training results dictionary
        """
        if num_episodes is None:
            num_episodes = config.maddpg.max_episodes
        
        print(f"Starting MADDPG training for {num_episodes} episodes...")
        print(f"Environment: {self.env.num_devices} devices, {self.env.num_servers} servers")
        print(f"State dim: {self.env.state_dim}, Action dim: {self.env.action_dim}")
        
        for episode in range(num_episodes):
            # Reset environment
            states = self.env.reset()
            self.maddpg.reset_noise()
            
            episode_reward = 0.0
            episode_energy = 0.0
            episode_delay = 0.0
            episode_violations = 0
            step_count = 0
            
            while step_count < self.env.max_steps:
                # Get actions from MADDPG
                actions = self.maddpg.act(states, add_noise=True)
                
                # Execute step
                next_states, rewards, dones, info = self.env.step(actions)
                
                # Store experience
                self.maddpg.step(states, actions, rewards, next_states, dones)
                
                # Accumulate statistics
                episode_reward += sum(rewards)
                episode_energy += sum(info['execution_results']['device_energies'])
                episode_delay += sum(info['execution_results']['device_delays'])
                
                # Count violations
                for violations in info['execution_results']['constraint_violations']:
                    episode_violations += sum(violations.values())
                
                step_count += 1
                states = next_states
                
                if all(dones):
                    break
            
            # Update noise scale
            self.maddpg.update_noise_scale(episode, num_episodes)
            
            # Store episode statistics
            self.training_stats['episode_rewards'].append(episode_reward)
            self.training_stats['episode_energies'].append(episode_energy)
            self.training_stats['episode_delays'].append(episode_delay)
            self.training_stats['episode_violations'].append(episode_violations)
            
            # Calculate average accuracy and battery levels
            avg_accuracy = np.mean([task.achieved_accuracy for task in self.env.tasks])
            avg_battery = np.mean([device.get_battery_ratio() for device in self.env.devices])
            
            self.training_stats['episode_accuracies'].append(avg_accuracy)
            self.training_stats['avg_battery_levels'].append(avg_battery)
            
            # Print progress
            if (episode + 1) % 10 == 0:
                print(f"Episode {episode + 1}/{num_episodes}: "
                      f"Reward={episode_reward:.2f}, "
                      f"Energy={episode_energy:.2f}, "
                      f"Delay={episode_delay:.4f}, "
                      f"Accuracy={avg_accuracy:.3f}, "
                      f"Battery={avg_battery:.3f}")
            
            # Save models
            if (episode + 1) % save_frequency == 0:
                model_path = os.path.join(self.save_dir, f"maddpg_episode_{episode + 1}.pth")
                self.maddpg.save_models(model_path)
                print(f"Saved model at episode {episode + 1}")
            
            # Evaluate performance
            if (episode + 1) % eval_frequency == 0:
                self._evaluate_current_policy(episode + 1)
        
        # Save final model and statistics
        final_model_path = os.path.join(self.save_dir, "maddpg_final.pth")
        self.maddpg.save_models(final_model_path)
        
        # Convert numpy types to Python types for JSON serialization
        json_stats = {}
        for key, value in self.training_stats.items():
            if isinstance(value, list):
                json_stats[key] = [float(x) if hasattr(x, 'item') else x for x in value]
            else:
                json_stats[key] = float(value) if hasattr(value, 'item') else value

        stats_path = os.path.join(self.save_dir, "training_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(json_stats, f, indent=2)
        
        print("Training completed!")
        return self.training_stats
    
    def _evaluate_current_policy(self, episode: int, num_eval_episodes: int = 10):
        """Evaluate current policy without exploration noise"""
        print(f"Evaluating policy at episode {episode}...")
        
        eval_rewards = []
        eval_energies = []
        eval_delays = []
        
        for _ in range(num_eval_episodes):
            states = self.env.reset()
            episode_reward = 0.0
            episode_energy = 0.0
            episode_delay = 0.0
            step_count = 0
            
            while step_count < self.env.max_steps:
                # Get actions without noise
                actions = self.maddpg.act(states, add_noise=False)
                next_states, rewards, dones, info = self.env.step(actions)
                
                episode_reward += sum(rewards)
                episode_energy += sum(info['execution_results']['device_energies'])
                episode_delay += sum(info['execution_results']['device_delays'])
                
                step_count += 1
                states = next_states
                
                if all(dones):
                    break
            
            eval_rewards.append(episode_reward)
            eval_energies.append(episode_energy)
            eval_delays.append(episode_delay)
        
        print(f"Evaluation results - Reward: {np.mean(eval_rewards):.2f} ± {np.std(eval_rewards):.2f}, "
              f"Energy: {np.mean(eval_energies):.2f} ± {np.std(eval_energies):.2f}, "
              f"Delay: {np.mean(eval_delays):.4f} ± {np.std(eval_delays):.4f}")
    
    def evaluate_baselines(self, num_episodes: int = 100) -> Dict[str, Any]:
        """Evaluate all baseline algorithms"""
        print("Evaluating baseline algorithms...")
        
        baseline_results = {}
        
        for name, baseline in self.baselines.items():
            print(f"Evaluating {name}...")
            results = evaluate_baseline(baseline, self.env, num_episodes)
            baseline_results[name] = results
            
            print(f"{name} - Reward: {results['avg_reward']:.2f} ± {results['std_reward']:.2f}, "
                  f"Energy: {results['avg_energy']:.2f} ± {results['std_energy']:.2f}, "
                  f"Delay: {results['avg_delay']:.4f} ± {results['std_delay']:.4f}")
        
        # Save baseline results
        baseline_path = os.path.join(self.save_dir, "baseline_results.json")
        with open(baseline_path, 'w') as f:
            json.dump(baseline_results, f, indent=2)
        
        return baseline_results
    
    def plot_training_curves(self):
        """Plot training curves"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # Episode rewards
        axes[0, 0].plot(self.training_stats['episode_rewards'])
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Total Reward')
        
        # Episode energies
        axes[0, 1].plot(self.training_stats['episode_energies'])
        axes[0, 1].set_title('Episode Energy Consumption')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Total Energy (J)')
        
        # Episode delays
        axes[0, 2].plot(self.training_stats['episode_delays'])
        axes[0, 2].set_title('Episode Delays')
        axes[0, 2].set_xlabel('Episode')
        axes[0, 2].set_ylabel('Total Delay (s)')
        
        # Episode accuracies
        axes[1, 0].plot(self.training_stats['episode_accuracies'])
        axes[1, 0].set_title('Episode Accuracies')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Average Accuracy')
        
        # Battery levels
        axes[1, 1].plot(self.training_stats['avg_battery_levels'])
        axes[1, 1].set_title('Average Battery Levels')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Battery Ratio')
        
        # Constraint violations
        axes[1, 2].plot(self.training_stats['episode_violations'])
        axes[1, 2].set_title('Constraint Violations')
        axes[1, 2].set_xlabel('Episode')
        axes[1, 2].set_ylabel('Total Violations')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_with_baselines(self, baseline_results: Dict[str, Any]):
        """Compare MADDPG with baseline algorithms"""
        # Prepare data for comparison
        algorithms = list(baseline_results.keys()) + ['MADDPG']
        
        # Calculate MADDPG final performance (last 10% of episodes)
        final_episodes = int(len(self.training_stats['episode_rewards']) * 0.1)
        maddpg_reward = np.mean(self.training_stats['episode_rewards'][-final_episodes:])
        maddpg_energy = np.mean(self.training_stats['episode_energies'][-final_episodes:])
        maddpg_delay = np.mean(self.training_stats['episode_delays'][-final_episodes:])
        maddpg_accuracy = np.mean(self.training_stats['episode_accuracies'][-final_episodes:])
        
        # Create comparison plots
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Rewards comparison
        rewards = [baseline_results[alg]['avg_reward'] for alg in baseline_results.keys()] + [maddpg_reward]
        axes[0, 0].bar(algorithms, rewards)
        axes[0, 0].set_title('Average Rewards Comparison')
        axes[0, 0].set_ylabel('Average Reward')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Energy comparison
        energies = [baseline_results[alg]['avg_energy'] for alg in baseline_results.keys()] + [maddpg_energy]
        axes[0, 1].bar(algorithms, energies)
        axes[0, 1].set_title('Average Energy Consumption Comparison')
        axes[0, 1].set_ylabel('Average Energy (J)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Delay comparison
        delays = [baseline_results[alg]['avg_delay'] for alg in baseline_results.keys()] + [maddpg_delay]
        axes[1, 0].bar(algorithms, delays)
        axes[1, 0].set_title('Average Delay Comparison')
        axes[1, 0].set_ylabel('Average Delay (s)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Accuracy comparison
        accuracies = [baseline_results[alg]['avg_accuracy'] for alg in baseline_results.keys()] + [maddpg_accuracy]
        axes[1, 1].bar(algorithms, accuracies)
        axes[1, 1].set_title('Average Accuracy Comparison')
        axes[1, 1].set_ylabel('Average Accuracy')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'baseline_comparison.png'), dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='MADDPG Training for Edge Computing Optimization')
    parser.add_argument('--episodes', type=int, default=1000, help='Number of training episodes')
    parser.add_argument('--eval_episodes', type=int, default=100, help='Number of evaluation episodes')
    parser.add_argument('--save_dir', type=str, default='results', help='Directory to save results')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--eval_only', action='store_true', help='Only evaluate baselines')
    
    args = parser.parse_args()
    
    # Set random seeds
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # Create environment
    env = EdgeComputingEnvironment(random_seed=args.seed)
    
    # Create trainer
    trainer = MADDPGTrainer(env, args.save_dir)
    
    if args.eval_only:
        # Only evaluate baselines
        baseline_results = trainer.evaluate_baselines(args.eval_episodes)
        print("Baseline evaluation completed!")
    else:
        # Train MADDPG
        training_stats = trainer.train(args.episodes)
        
        # Plot training curves
        trainer.plot_training_curves()
        
        # Evaluate baselines
        baseline_results = trainer.evaluate_baselines(args.eval_episodes)
        
        # Compare with baselines
        trainer.compare_with_baselines(baseline_results)
        
        print(f"Training and evaluation completed! Results saved in {args.save_dir}")

if __name__ == "__main__":
    main()

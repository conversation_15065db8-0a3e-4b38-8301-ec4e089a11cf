"""
Visualization utilities for analysis and plotting
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
import os

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class VisualizationManager:
    """Manager for creating various plots and visualizations"""
    
    def __init__(self, save_dir: str = "plots"):
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
    
    def plot_accuracy_performance_tradeoff(self, results: Dict[str, Any], 
                                         save_name: str = "accuracy_performance_tradeoff.png"):
        """Plot accuracy vs performance trade-off analysis"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        algorithms = list(results.keys())
        accuracies = [results[alg]['avg_accuracy'] for alg in algorithms]
        energies = [results[alg]['avg_energy'] for alg in algorithms]
        delays = [results[alg]['avg_delay'] for alg in algorithms]
        
        # Accuracy vs Energy
        axes[0].scatter(accuracies, energies, s=100, alpha=0.7)
        for i, alg in enumerate(algorithms):
            axes[0].annotate(alg, (accuracies[i], energies[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
        axes[0].set_xlabel('Average Accuracy')
        axes[0].set_ylabel('Average Energy (J)')
        axes[0].set_title('Accuracy vs Energy Trade-off')
        axes[0].grid(True, alpha=0.3)
        
        # Accuracy vs Delay
        axes[1].scatter(accuracies, delays, s=100, alpha=0.7)
        for i, alg in enumerate(algorithms):
            axes[1].annotate(alg, (accuracies[i], delays[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
        axes[1].set_xlabel('Average Accuracy')
        axes[1].set_ylabel('Average Delay (s)')
        axes[1].set_title('Accuracy vs Delay Trade-off')
        axes[1].grid(True, alpha=0.3)
        
        # Energy vs Delay
        axes[2].scatter(energies, delays, s=100, alpha=0.7)
        for i, alg in enumerate(algorithms):
            axes[2].annotate(alg, (energies[i], delays[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
        axes[2].set_xlabel('Average Energy (J)')
        axes[2].set_ylabel('Average Delay (s)')
        axes[2].set_title('Energy vs Delay Trade-off')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_constraint_violations(self, results: Dict[str, Any],
                                 save_name: str = "constraint_violations.png"):
        """Plot constraint violation analysis"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        algorithms = list(results.keys())
        violation_types = ['accuracy', 'delay', 'battery', 'server']
        
        for i, violation_type in enumerate(violation_types):
            ax = axes[i // 2, i % 2]
            
            violation_rates = [results[alg]['violation_rate'][violation_type] 
                             for alg in algorithms]
            
            bars = ax.bar(algorithms, violation_rates)
            ax.set_title(f'{violation_type.capitalize()} Constraint Violations')
            ax.set_ylabel('Violation Rate')
            ax.tick_params(axis='x', rotation=45)
            
            # Add value labels on bars
            for bar, rate in zip(bars, violation_rates):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{rate:.3f}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_battery_analysis(self, training_stats: Dict[str, Any],
                            save_name: str = "battery_analysis.png"):
        """Plot battery level analysis over training"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        episodes = range(len(training_stats['avg_battery_levels']))
        
        # Battery levels over time
        axes[0, 0].plot(episodes, training_stats['avg_battery_levels'])
        axes[0, 0].set_title('Average Battery Levels Over Training')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Average Battery Ratio')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Energy consumption over time
        axes[0, 1].plot(episodes, training_stats['episode_energies'])
        axes[0, 1].set_title('Energy Consumption Over Training')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Total Energy (J)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Battery vs Energy correlation
        axes[1, 0].scatter(training_stats['avg_battery_levels'], 
                          training_stats['episode_energies'], alpha=0.5)
        axes[1, 0].set_title('Battery Level vs Energy Consumption')
        axes[1, 0].set_xlabel('Average Battery Ratio')
        axes[1, 0].set_ylabel('Total Energy (J)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Battery vs Reward correlation
        axes[1, 1].scatter(training_stats['avg_battery_levels'], 
                          training_stats['episode_rewards'], alpha=0.5)
        axes[1, 1].set_title('Battery Level vs Reward')
        axes[1, 1].set_xlabel('Average Battery Ratio')
        axes[1, 1].set_ylabel('Total Reward')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_convergence_analysis(self, training_stats: Dict[str, Any],
                                window_size: int = 50,
                                save_name: str = "convergence_analysis.png"):
        """Plot convergence analysis with moving averages"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        episodes = range(len(training_stats['episode_rewards']))
        
        # Moving average function
        def moving_average(data, window):
            return pd.Series(data).rolling(window=window, min_periods=1).mean()
        
        # Rewards convergence
        rewards_ma = moving_average(training_stats['episode_rewards'], window_size)
        axes[0, 0].plot(episodes, training_stats['episode_rewards'], alpha=0.3, label='Raw')
        axes[0, 0].plot(episodes, rewards_ma, label=f'MA({window_size})')
        axes[0, 0].set_title('Reward Convergence')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Total Reward')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Energy convergence
        energy_ma = moving_average(training_stats['episode_energies'], window_size)
        axes[0, 1].plot(episodes, training_stats['episode_energies'], alpha=0.3, label='Raw')
        axes[0, 1].plot(episodes, energy_ma, label=f'MA({window_size})')
        axes[0, 1].set_title('Energy Convergence')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Total Energy (J)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Delay convergence
        delay_ma = moving_average(training_stats['episode_delays'], window_size)
        axes[1, 0].plot(episodes, training_stats['episode_delays'], alpha=0.3, label='Raw')
        axes[1, 0].plot(episodes, delay_ma, label=f'MA({window_size})')
        axes[1, 0].set_title('Delay Convergence')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Total Delay (s)')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # Accuracy convergence
        accuracy_ma = moving_average(training_stats['episode_accuracies'], window_size)
        axes[1, 1].plot(episodes, training_stats['episode_accuracies'], alpha=0.3, label='Raw')
        axes[1, 1].plot(episodes, accuracy_ma, label=f'MA({window_size})')
        axes[1, 1].set_title('Accuracy Convergence')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Average Accuracy')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_adaptive_weight_analysis(self, battery_ratios: List[float],
                                    adaptive_weights: List[float],
                                    save_name: str = "adaptive_weight_analysis.png"):
        """Plot adaptive weight mechanism analysis"""
        from performance import AdaptiveWeightMechanism
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # Weight curve
        weight_mechanism = AdaptiveWeightMechanism()
        battery_range, weight_curve = weight_mechanism.simulate_weight_curve()
        
        axes[0].plot(battery_range, weight_curve, 'b-', linewidth=2, label='Weight Function')
        axes[0].axhline(y=0.5, color='r', linestyle='--', alpha=0.7, label='Balanced Point')
        axes[0].axvline(x=weight_mechanism.theta, color='g', linestyle='--', alpha=0.7, 
                       label=f'Threshold (θ={weight_mechanism.theta})')
        axes[0].set_xlabel('Battery Ratio')
        axes[0].set_ylabel('Adaptive Weight (α)')
        axes[0].set_title('Adaptive Weight Function')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Actual weight distribution during training
        if battery_ratios and adaptive_weights:
            axes[1].scatter(battery_ratios, adaptive_weights, alpha=0.6)
            axes[1].plot(battery_range, weight_curve, 'r-', linewidth=2, label='Theoretical')
            axes[1].set_xlabel('Battery Ratio')
            axes[1].set_ylabel('Adaptive Weight (α)')
            axes[1].set_title('Actual vs Theoretical Weights')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_summary_report(self, training_stats: Dict[str, Any],
                            baseline_results: Dict[str, Any],
                            save_name: str = "summary_report.png"):
        """Create a comprehensive summary report"""
        fig = plt.figure(figsize=(16, 12))
        
        # Create a grid layout
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # Training curves (top row)
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.plot(training_stats['episode_rewards'])
        ax1.set_title('Training Rewards')
        ax1.set_xlabel('Episode')
        ax1.set_ylabel('Reward')
        
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.plot(training_stats['episode_energies'])
        ax2.set_title('Training Energy')
        ax2.set_xlabel('Episode')
        ax2.set_ylabel('Energy (J)')
        
        ax3 = fig.add_subplot(gs[0, 2])
        ax3.plot(training_stats['episode_delays'])
        ax3.set_title('Training Delay')
        ax3.set_xlabel('Episode')
        ax3.set_ylabel('Delay (s)')
        
        ax4 = fig.add_subplot(gs[0, 3])
        ax4.plot(training_stats['avg_battery_levels'])
        ax4.set_title('Battery Levels')
        ax4.set_xlabel('Episode')
        ax4.set_ylabel('Battery Ratio')
        
        # Baseline comparison (middle row)
        algorithms = list(baseline_results.keys())
        
        ax5 = fig.add_subplot(gs[1, 0])
        rewards = [baseline_results[alg]['avg_reward'] for alg in algorithms]
        ax5.bar(algorithms, rewards)
        ax5.set_title('Baseline Rewards')
        ax5.tick_params(axis='x', rotation=45)
        
        ax6 = fig.add_subplot(gs[1, 1])
        energies = [baseline_results[alg]['avg_energy'] for alg in algorithms]
        ax6.bar(algorithms, energies)
        ax6.set_title('Baseline Energy')
        ax6.tick_params(axis='x', rotation=45)
        
        ax7 = fig.add_subplot(gs[1, 2])
        delays = [baseline_results[alg]['avg_delay'] for alg in algorithms]
        ax7.bar(algorithms, delays)
        ax7.set_title('Baseline Delay')
        ax7.tick_params(axis='x', rotation=45)
        
        ax8 = fig.add_subplot(gs[1, 3])
        accuracies = [baseline_results[alg]['avg_accuracy'] for alg in algorithms]
        ax8.bar(algorithms, accuracies)
        ax8.set_title('Baseline Accuracy')
        ax8.tick_params(axis='x', rotation=45)
        
        # Performance analysis (bottom row)
        ax9 = fig.add_subplot(gs[2, :2])
        ax9.scatter(energies, delays, s=100, alpha=0.7)
        for i, alg in enumerate(algorithms):
            ax9.annotate(alg, (energies[i], delays[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        ax9.set_xlabel('Energy (J)')
        ax9.set_ylabel('Delay (s)')
        ax9.set_title('Energy vs Delay Trade-off')
        ax9.grid(True, alpha=0.3)
        
        ax10 = fig.add_subplot(gs[2, 2:])
        violation_types = ['accuracy', 'delay', 'battery', 'server']
        violation_data = []
        for alg in algorithms:
            alg_violations = [baseline_results[alg]['violation_rate'][vtype] 
                            for vtype in violation_types]
            violation_data.append(alg_violations)
        
        x = np.arange(len(violation_types))
        width = 0.8 / len(algorithms)
        
        for i, (alg, violations) in enumerate(zip(algorithms, violation_data)):
            ax10.bar(x + i * width, violations, width, label=alg, alpha=0.8)
        
        ax10.set_xlabel('Constraint Type')
        ax10.set_ylabel('Violation Rate')
        ax10.set_title('Constraint Violations by Algorithm')
        ax10.set_xticks(x + width * (len(algorithms) - 1) / 2)
        ax10.set_xticklabels(violation_types)
        ax10.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()

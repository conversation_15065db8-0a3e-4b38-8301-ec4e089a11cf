Layer 0: Conv2d, Input: [3, 32, 32], Output: [64, 32, 32], FLOPs: 1769472.0, Params: 1728, Cumulative FLOPs: 1769472.0
Layer 1: BatchNorm2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 65536, Params: 128, Cumulative FLOPs: 1835008.0
Layer 2: ReLU, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 1835008.0
Layer 3: Identity, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 1835008.0
Layer 4: Conv2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 4194304.0, Params: 4096, Cumulative FLOPs: 6029312.0
Layer 5: BatchNorm2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 65536, Params: 128, Cumulative FLOPs: 6094848.0
Layer 6: ReLU, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 6094848.0
Layer 7: Conv2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 37748736.0, Params: 36864, Cumulative FLOPs: 43843584.0
Layer 8: BatchNorm2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 65536, Params: 128, Cumulative FLOPs: 43909120.0
Layer 9: ReLU, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 43909120.0
Layer 10: Conv2d, Input: [64, 32, 32], Output: [256, 32, 32], FLOPs: 16777216.0, Params: 16384, Cumulative FLOPs: 60686336.0
Layer 11: BatchNorm2d, Input: [256, 32, 32], Output: [256, 32, 32], FLOPs: 262144, Params: 512, Cumulative FLOPs: 60948480.0
Layer 12: Conv2d, Input: [64, 32, 32], Output: [256, 32, 32], FLOPs: 16777216.0, Params: 16384, Cumulative FLOPs: 77725696.0
Layer 13: BatchNorm2d, Input: [256, 32, 32], Output: [256, 32, 32], FLOPs: 262144, Params: 512, Cumulative FLOPs: 77987840.0
Layer 14: ReLU, Input: [256, 32, 32], Output: [256, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 77987840.0
Layer 15: Conv2d, Input: [256, 32, 32], Output: [64, 32, 32], FLOPs: 16777216.0, Params: 16384, Cumulative FLOPs: 94765056.0
Layer 16: BatchNorm2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 65536, Params: 128, Cumulative FLOPs: 94830592.0
Layer 17: ReLU, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 94830592.0
Layer 18: Conv2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 37748736.0, Params: 36864, Cumulative FLOPs: 132579328.0
Layer 19: BatchNorm2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 65536, Params: 128, Cumulative FLOPs: 132644864.0
Layer 20: ReLU, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 132644864.0
Layer 21: Conv2d, Input: [64, 32, 32], Output: [256, 32, 32], FLOPs: 16777216.0, Params: 16384, Cumulative FLOPs: 149422080.0
Layer 22: BatchNorm2d, Input: [256, 32, 32], Output: [256, 32, 32], FLOPs: 262144, Params: 512, Cumulative FLOPs: 149684224.0
Layer 23: ReLU, Input: [256, 32, 32], Output: [256, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 149684224.0
Layer 24: Conv2d, Input: [256, 32, 32], Output: [64, 32, 32], FLOPs: 16777216.0, Params: 16384, Cumulative FLOPs: 166461440.0
Layer 25: BatchNorm2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 65536, Params: 128, Cumulative FLOPs: 166526976.0
Layer 26: ReLU, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 166526976.0
Layer 27: Conv2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 37748736.0, Params: 36864, Cumulative FLOPs: 204275712.0
Layer 28: BatchNorm2d, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 65536, Params: 128, Cumulative FLOPs: 204341248.0
Layer 29: ReLU, Input: [64, 32, 32], Output: [64, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 204341248.0
Layer 30: Conv2d, Input: [64, 32, 32], Output: [256, 32, 32], FLOPs: 16777216.0, Params: 16384, Cumulative FLOPs: 221118464.0
Layer 31: BatchNorm2d, Input: [256, 32, 32], Output: [256, 32, 32], FLOPs: 262144, Params: 512, Cumulative FLOPs: 221380608.0
Layer 32: ReLU, Input: [256, 32, 32], Output: [256, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 221380608.0
Layer 33: Conv2d, Input: [256, 32, 32], Output: [128, 32, 32], FLOPs: 33554432.0, Params: 32768, Cumulative FLOPs: 254935040.0
Layer 34: BatchNorm2d, Input: [128, 32, 32], Output: [128, 32, 32], FLOPs: 131072, Params: 256, Cumulative FLOPs: 255066112.0
Layer 35: ReLU, Input: [128, 32, 32], Output: [128, 32, 32], FLOPs: 0, Params: 0, Cumulative FLOPs: 255066112.0
Layer 36: Conv2d, Input: [128, 32, 32], Output: [128, 16, 16], FLOPs: 150994944.0, Params: 147456, Cumulative FLOPs: 406061056.0
Layer 37: BatchNorm2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 32768, Params: 256, Cumulative FLOPs: 406093824.0
Layer 38: ReLU, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 406093824.0
Layer 39: Conv2d, Input: [128, 16, 16], Output: [512, 16, 16], FLOPs: 16777216.0, Params: 65536, Cumulative FLOPs: 422871040.0
Layer 40: BatchNorm2d, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 131072, Params: 1024, Cumulative FLOPs: 423002112.0
Layer 41: Conv2d, Input: [256, 32, 32], Output: [512, 16, 16], FLOPs: 134217728.0, Params: 131072, Cumulative FLOPs: 557219840.0
Layer 42: BatchNorm2d, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 131072, Params: 1024, Cumulative FLOPs: 557350912.0
Layer 43: ReLU, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 557350912.0
Layer 44: Conv2d, Input: [512, 16, 16], Output: [128, 16, 16], FLOPs: 16777216.0, Params: 65536, Cumulative FLOPs: 574128128.0
Layer 45: BatchNorm2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 32768, Params: 256, Cumulative FLOPs: 574160896.0
Layer 46: ReLU, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 574160896.0
Layer 47: Conv2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 37748736.0, Params: 147456, Cumulative FLOPs: 611909632.0
Layer 48: BatchNorm2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 32768, Params: 256, Cumulative FLOPs: 611942400.0
Layer 49: ReLU, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 611942400.0
Layer 50: Conv2d, Input: [128, 16, 16], Output: [512, 16, 16], FLOPs: 16777216.0, Params: 65536, Cumulative FLOPs: 628719616.0
Layer 51: BatchNorm2d, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 131072, Params: 1024, Cumulative FLOPs: 628850688.0
Layer 52: ReLU, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 628850688.0
Layer 53: Conv2d, Input: [512, 16, 16], Output: [128, 16, 16], FLOPs: 16777216.0, Params: 65536, Cumulative FLOPs: 645627904.0
Layer 54: BatchNorm2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 32768, Params: 256, Cumulative FLOPs: 645660672.0
Layer 55: ReLU, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 645660672.0
Layer 56: Conv2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 37748736.0, Params: 147456, Cumulative FLOPs: 683409408.0
Layer 57: BatchNorm2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 32768, Params: 256, Cumulative FLOPs: 683442176.0
Layer 58: ReLU, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 683442176.0
Layer 59: Conv2d, Input: [128, 16, 16], Output: [512, 16, 16], FLOPs: 16777216.0, Params: 65536, Cumulative FLOPs: 700219392.0
Layer 60: BatchNorm2d, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 131072, Params: 1024, Cumulative FLOPs: 700350464.0
Layer 61: ReLU, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 700350464.0
Layer 62: Conv2d, Input: [512, 16, 16], Output: [128, 16, 16], FLOPs: 16777216.0, Params: 65536, Cumulative FLOPs: 717127680.0
Layer 63: BatchNorm2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 32768, Params: 256, Cumulative FLOPs: 717160448.0
Layer 64: ReLU, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 717160448.0
Layer 65: Conv2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 37748736.0, Params: 147456, Cumulative FLOPs: 754909184.0
Layer 66: BatchNorm2d, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 32768, Params: 256, Cumulative FLOPs: 754941952.0
Layer 67: ReLU, Input: [128, 16, 16], Output: [128, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 754941952.0
Layer 68: Conv2d, Input: [128, 16, 16], Output: [512, 16, 16], FLOPs: 16777216.0, Params: 65536, Cumulative FLOPs: 771719168.0
Layer 69: BatchNorm2d, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 131072, Params: 1024, Cumulative FLOPs: 771850240.0
Layer 70: ReLU, Input: [512, 16, 16], Output: [512, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 771850240.0
Layer 71: AdaptiveAvgPool2d, Input: [512, 16, 16], Output: [512, 1, 1], FLOPs: 0, Params: 0, Cumulative FLOPs: 771850240.0
Layer 72: Flatten, Input: [512, 1, 1], Output: [512], FLOPs: 0, Params: 0, Cumulative FLOPs: 771850240.0
Layer 73: Linear, Input: [512], Output: [10], FLOPs: 5120, Params: 5130, Cumulative FLOPs: 771855360.0 [EARLY EXIT POINT]
Layer 74: Conv2d, Input: [512, 16, 16], Output: [256, 16, 16], FLOPs: 33554432.0, Params: 131072, Cumulative FLOPs: 805409792.0
Layer 75: BatchNorm2d, Input: [256, 16, 16], Output: [256, 16, 16], FLOPs: 65536, Params: 512, Cumulative FLOPs: 805475328.0
Layer 76: ReLU, Input: [256, 16, 16], Output: [256, 16, 16], FLOPs: 0, Params: 0, Cumulative FLOPs: 805475328.0
Layer 77: Conv2d, Input: [256, 16, 16], Output: [256, 8, 8], FLOPs: 150994944.0, Params: 589824, Cumulative FLOPs: 956470272.0
Layer 78: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 956486656.0
Layer 79: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 956486656.0
Layer 80: Conv2d, Input: [256, 8, 8], Output: [1024, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 973263872.0
Layer 81: BatchNorm2d, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 65536, Params: 2048, Cumulative FLOPs: 973329408.0
Layer 82: Conv2d, Input: [512, 16, 16], Output: [1024, 8, 8], FLOPs: 134217728.0, Params: 524288, Cumulative FLOPs: 1107547136.0
Layer 83: BatchNorm2d, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 65536, Params: 2048, Cumulative FLOPs: 1107612672.0
Layer 84: ReLU, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1107612672.0
Layer 85: Conv2d, Input: [1024, 8, 8], Output: [256, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1124389888.0
Layer 86: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1124406272.0
Layer 87: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1124406272.0
Layer 88: Conv2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 37748736.0, Params: 589824, Cumulative FLOPs: 1162155008.0
Layer 89: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1162171392.0
Layer 90: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1162171392.0
Layer 91: Conv2d, Input: [256, 8, 8], Output: [1024, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1178948608.0
Layer 92: BatchNorm2d, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 65536, Params: 2048, Cumulative FLOPs: 1179014144.0
Layer 93: ReLU, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1179014144.0
Layer 94: Conv2d, Input: [1024, 8, 8], Output: [256, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1195791360.0
Layer 95: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1195807744.0
Layer 96: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1195807744.0
Layer 97: Conv2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 37748736.0, Params: 589824, Cumulative FLOPs: 1233556480.0
Layer 98: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1233572864.0
Layer 99: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1233572864.0
Layer 100: Conv2d, Input: [256, 8, 8], Output: [1024, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1250350080.0
Layer 101: BatchNorm2d, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 65536, Params: 2048, Cumulative FLOPs: 1250415616.0
Layer 102: ReLU, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1250415616.0
Layer 103: AdaptiveAvgPool2d, Input: [1024, 8, 8], Output: [1024, 1, 1], FLOPs: 0, Params: 0, Cumulative FLOPs: 1250415616.0
Layer 104: Flatten, Input: [1024, 1, 1], Output: [1024], FLOPs: 0, Params: 0, Cumulative FLOPs: 1250415616.0
Layer 105: Linear, Input: [1024], Output: [10], FLOPs: 10240, Params: 10250, Cumulative FLOPs: 1250425856.0 [EARLY EXIT POINT]
Layer 106: Conv2d, Input: [1024, 8, 8], Output: [256, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1267203072.0
Layer 107: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1267219456.0
Layer 108: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1267219456.0
Layer 109: Conv2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 37748736.0, Params: 589824, Cumulative FLOPs: 1304968192.0
Layer 110: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1304984576.0
Layer 111: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1304984576.0
Layer 112: Conv2d, Input: [256, 8, 8], Output: [1024, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1321761792.0
Layer 113: BatchNorm2d, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 65536, Params: 2048, Cumulative FLOPs: 1321827328.0
Layer 114: ReLU, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1321827328.0
Layer 115: Conv2d, Input: [1024, 8, 8], Output: [256, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1338604544.0
Layer 116: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1338620928.0
Layer 117: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1338620928.0
Layer 118: Conv2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 37748736.0, Params: 589824, Cumulative FLOPs: 1376369664.0
Layer 119: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1376386048.0
Layer 120: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1376386048.0
Layer 121: Conv2d, Input: [256, 8, 8], Output: [1024, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1393163264.0
Layer 122: BatchNorm2d, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 65536, Params: 2048, Cumulative FLOPs: 1393228800.0
Layer 123: ReLU, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1393228800.0
Layer 124: Conv2d, Input: [1024, 8, 8], Output: [256, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1410006016.0
Layer 125: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1410022400.0
Layer 126: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1410022400.0
Layer 127: Conv2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 37748736.0, Params: 589824, Cumulative FLOPs: 1447771136.0
Layer 128: BatchNorm2d, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 16384, Params: 512, Cumulative FLOPs: 1447787520.0
Layer 129: ReLU, Input: [256, 8, 8], Output: [256, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1447787520.0
Layer 130: Conv2d, Input: [256, 8, 8], Output: [1024, 8, 8], FLOPs: 16777216.0, Params: 262144, Cumulative FLOPs: 1464564736.0
Layer 131: BatchNorm2d, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 65536, Params: 2048, Cumulative FLOPs: 1464630272.0
Layer 132: ReLU, Input: [1024, 8, 8], Output: [1024, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1464630272.0
Layer 133: AdaptiveAvgPool2d, Input: [1024, 8, 8], Output: [1024, 1, 1], FLOPs: 0, Params: 0, Cumulative FLOPs: 1464630272.0
Layer 134: Flatten, Input: [1024, 1, 1], Output: [1024], FLOPs: 0, Params: 0, Cumulative FLOPs: 1464630272.0
Layer 135: Linear, Input: [1024], Output: [10], FLOPs: 10240, Params: 10250, Cumulative FLOPs: 1464640512.0 [EARLY EXIT POINT]
Layer 136: Conv2d, Input: [1024, 8, 8], Output: [512, 8, 8], FLOPs: 33554432.0, Params: 524288, Cumulative FLOPs: 1498194944.0
Layer 137: BatchNorm2d, Input: [512, 8, 8], Output: [512, 8, 8], FLOPs: 32768, Params: 1024, Cumulative FLOPs: 1498227712.0
Layer 138: ReLU, Input: [512, 8, 8], Output: [512, 8, 8], FLOPs: 0, Params: 0, Cumulative FLOPs: 1498227712.0
Layer 139: Conv2d, Input: [512, 8, 8], Output: [512, 4, 4], FLOPs: 150994944.0, Params: 2359296, Cumulative FLOPs: 1649222656.0
Layer 140: BatchNorm2d, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 8192, Params: 1024, Cumulative FLOPs: 1649230848.0
Layer 141: ReLU, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1649230848.0
Layer 142: Conv2d, Input: [512, 4, 4], Output: [2048, 4, 4], FLOPs: 16777216.0, Params: 1048576, Cumulative FLOPs: 1666008064.0
Layer 143: BatchNorm2d, Input: [2048, 4, 4], Output: [2048, 4, 4], FLOPs: 32768, Params: 4096, Cumulative FLOPs: 1666040832.0
Layer 144: Conv2d, Input: [1024, 8, 8], Output: [2048, 4, 4], FLOPs: 134217728.0, Params: 2097152, Cumulative FLOPs: 1800258560.0
Layer 145: BatchNorm2d, Input: [2048, 4, 4], Output: [2048, 4, 4], FLOPs: 32768, Params: 4096, Cumulative FLOPs: 1800291328.0
Layer 146: ReLU, Input: [2048, 4, 4], Output: [2048, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1800291328.0
Layer 147: Conv2d, Input: [2048, 4, 4], Output: [512, 4, 4], FLOPs: 16777216.0, Params: 1048576, Cumulative FLOPs: 1817068544.0
Layer 148: BatchNorm2d, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 8192, Params: 1024, Cumulative FLOPs: 1817076736.0
Layer 149: ReLU, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1817076736.0
Layer 150: Conv2d, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 37748736.0, Params: 2359296, Cumulative FLOPs: 1854825472.0
Layer 151: BatchNorm2d, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 8192, Params: 1024, Cumulative FLOPs: 1854833664.0
Layer 152: ReLU, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1854833664.0
Layer 153: Conv2d, Input: [512, 4, 4], Output: [2048, 4, 4], FLOPs: 16777216.0, Params: 1048576, Cumulative FLOPs: 1871610880.0
Layer 154: BatchNorm2d, Input: [2048, 4, 4], Output: [2048, 4, 4], FLOPs: 32768, Params: 4096, Cumulative FLOPs: 1871643648.0
Layer 155: ReLU, Input: [2048, 4, 4], Output: [2048, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1871643648.0
Layer 156: Conv2d, Input: [2048, 4, 4], Output: [512, 4, 4], FLOPs: 16777216.0, Params: 1048576, Cumulative FLOPs: 1888420864.0
Layer 157: BatchNorm2d, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 8192, Params: 1024, Cumulative FLOPs: 1888429056.0
Layer 158: ReLU, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1888429056.0
Layer 159: Conv2d, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 37748736.0, Params: 2359296, Cumulative FLOPs: 1926177792.0
Layer 160: BatchNorm2d, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 8192, Params: 1024, Cumulative FLOPs: 1926185984.0
Layer 161: ReLU, Input: [512, 4, 4], Output: [512, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1926185984.0
Layer 162: Conv2d, Input: [512, 4, 4], Output: [2048, 4, 4], FLOPs: 16777216.0, Params: 1048576, Cumulative FLOPs: 1942963200.0
Layer 163: BatchNorm2d, Input: [2048, 4, 4], Output: [2048, 4, 4], FLOPs: 32768, Params: 4096, Cumulative FLOPs: 1942995968.0
Layer 164: ReLU, Input: [2048, 4, 4], Output: [2048, 4, 4], FLOPs: 0, Params: 0, Cumulative FLOPs: 1942995968.0
Layer 165: AdaptiveAvgPool2d, Input: [2048, 4, 4], Output: [2048, 1, 1], FLOPs: 0, Params: 0, Cumulative FLOPs: 1942995968.0
Layer 166: Linear, Input: [2048], Output: [10], FLOPs: 20480, Params: 20490, Cumulative FLOPs: 1943016448.0

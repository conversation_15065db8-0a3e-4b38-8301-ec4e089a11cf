# Heterogeneous Multi-Edge Server DNN Inference Optimization Framework

A comprehensive optimization framework for deep neural network inference across heterogeneous multi-edge servers with battery-aware energy and latency optimization using Multi-Agent Deep Deterministic Policy Gradient (MADDPG).

## Overview

This framework addresses the challenge of optimizing DNN inference in edge computing environments where multiple heterogeneous devices collaborate with edge servers. The system implements:

- **Battery-aware optimization** with adaptive weight mechanisms
- **Early exit strategies** for ResNet-50 on CIFAR-10
- **Partition point selection** (convolutional layers only)
- **Multi-agent reinforcement learning** using MADDPG
- **Comprehensive baseline comparisons**

## Key Features

### 🔋 Battery-Aware Optimization
- Adaptive weight function: `α_m = 1/(1+exp(-β(B_m/B_max - θ)))`
- Dynamic energy-delay trade-off based on remaining battery power
- Real-time battery monitoring and constraint enforcement

### 🧠 ResNet-50 with Early Exits
- 5 early exit points with validated accuracy levels (77% - 91.33%)
- Partition points restricted to convolutional layers only
- CIFAR-10 dataset with comprehensive layer analysis

### 🌐 Multi-Agent Environment
- 4 heterogeneous devices (NVIDIA Jetson Xavier)
- 4 edge servers (laptop 4060 specifications)
- Dynamic network conditions and server load balancing

### 🤖 MADDPG Algorithm
- Centralized training, decentralized execution
- Individual reward design for each agent
- Experience replay and target networks
- Noise scheduling for exploration

## Architecture

```
├── config.py                 # Configuration parameters
├── resnet_layer_info.py      # ResNet-50 layer specifications
├── models/                   # Core system models
│   ├── device.py            # LocalDevice class
│   ├── edge_server.py       # EdgeServer class
│   └── inference_task.py    # EdgeInferenceTask class
├── performance/             # Performance models
│   ├── energy_model.py      # Energy consumption calculations
│   ├── delay_model.py       # Latency calculations
│   └── adaptive_weight.py   # Battery-aware weight mechanism
├── networks/                # Neural network implementations
│   ├── resnet50_early_exit.py # ResNet-50 with early exits
│   └── maddpg.py           # MADDPG algorithm
├── environment/             # RL environment
│   └── edge_env.py         # Multi-agent edge environment
├── utils/                   # Utilities
│   └── visualization.py    # Plotting and analysis tools
├── baselines.py            # Baseline algorithms
└── main.py                 # Training and evaluation script
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd heterogeneous-edge-optimization
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Training MADDPG

```bash
python main.py --episodes 1000 --save_dir results --seed 42
```

### Evaluating Baselines Only

```bash
python main.py --eval_only --eval_episodes 100 --save_dir results
```

### Custom Configuration

```bash
python main.py --episodes 500 --eval_episodes 50 --save_dir custom_results --seed 123
```

## Configuration

Key parameters can be modified in `config.py`:

### Device Parameters
- Battery capacity: 30-50 mAh
- GPU frequency: [0.12, 1.10] GHz
- Energy coefficient: 1.3 W/GHz³

### Server Parameters
- Frequency: [2.2, 2.8] GHz
- Max load: 0.75
- Number of servers: 4

### Optimization Parameters
- Adaptive weight β: 10.0, θ: 0.4
- Accuracy range: [0.7, 0.91]
- Max delay: 50ms

## Baseline Algorithms

The framework includes 6 baseline algorithms for comparison:

1. **Local-Only**: All tasks executed locally
2. **Edge-Only**: All tasks fully offloaded
3. **Greedy-Accuracy**: Prioritizes maximum accuracy
4. **Greedy-Energy**: Minimizes energy consumption
5. **Random**: Random decision making
6. **Adaptive-Greedy**: Adapts based on system state

## Results and Visualization

The framework generates comprehensive visualizations:

- Training convergence curves
- Baseline performance comparisons
- Accuracy-performance trade-off analysis
- Constraint violation analysis
- Battery level analysis
- Adaptive weight mechanism visualization

## Technical Specifications

### Mathematical Models

**Energy Model:**
```
E_total = κ*f³*T_local + P_tx*D_tx/R + P_static*T_total
```

**Delay Model:**
```
T_total = C_local/(f*g) + D_tx/R + C_edge/(f_server*g_server)
```

**Objective Function:**
```
J = (1/M)*Σ[α_m*T_m + (1-α_m)*E_m]
```

### Constraints

1. **Accuracy**: `Acc(e_m) >= Acc_min`
2. **Battery**: `B_m(t) >= B_min`
3. **Frequency**: `f_m ∈ [f_min, f_max]`
4. **Server Capacity**: Load constraints
5. **Delay**: `T_m(t) <= T_max`

## Early Exit Points and Accuracies

| Exit Point | Layer | Accuracy |
|------------|-------|----------|
| Exit_1     | 73    | 77.00%   |
| Exit_2     | 105   | 87.31%   |
| Exit_3     | 135   | 90.38%   |
| Exit_4     | 150   | 91.13%   |
| Exit_5     | 166   | 91.21%   |
| Full       | 166   | 91.33%   |

## Valid Partition Points

Only convolutional layers can be used as partition points:
```
[0, 4, 7, 10, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 41, 44, 47, 50, 
 53, 56, 59, 62, 65, 68, 74, 77, 80, 82, 85, 88, 91, 94, 97, 100, 
 106, 109, 112, 115, 118, 121, 124, 127, 130, 136, 139, 142, 144, 
 147, 150, 153, 156, 159, 162]
```

## File Structure

- **Configuration**: `config.py`, `resnet_layer_info.py`
- **Core Models**: `models/` directory
- **Performance Models**: `performance/` directory  
- **Neural Networks**: `networks/` directory
- **Environment**: `environment/` directory
- **Utilities**: `utils/` directory
- **Main Scripts**: `main.py`, `baselines.py`

## Contributing

1. Follow the existing code structure and documentation style
2. Add comprehensive tests for new features
3. Update configuration parameters as needed
4. Ensure all constraints are properly validated

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Citation

If you use this framework in your research, please cite:

```bibtex
@article{heterogeneous_edge_optimization,
  title={Heterogeneous Multi-Edge Server DNN Inference Optimization Framework},
  author={Your Name},
  journal={Your Journal},
  year={2024}
}
```

## Acknowledgments

- Based on technical specifications for battery-aware edge computing optimization
- Implements MADDPG algorithm for multi-agent coordination
- Uses ResNet-50 architecture with CIFAR-10 dataset
- Incorporates real-world device and server specifications

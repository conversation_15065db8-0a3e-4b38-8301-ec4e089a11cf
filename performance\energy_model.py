"""
Energy Consumption Model for Heterogeneous Multi-Edge Server DNN Inference
Implements comprehensive energy calculations based on technical specifications
"""

import numpy as np
from typing import Dict, Any, Tuple
from config import config

class EnergyModel:
    """
    Energy consumption model implementing the mathematical formulations
    from the technical specification document
    """
    
    def __init__(self):
        """Initialize energy model with configuration parameters"""
        self.device_config = config.device
        self.network_config = config.network
        
    def calculate_computation_energy(self, frequency: float, computation_time: float, 
                                   kappa: float = None) -> float:
        """
        Calculate computation energy consumption
        E_comp = κ * f^3 * T_local
        
        Args:
            frequency: GPU frequency in Hz
            computation_time: Local computation time in seconds
            kappa: Energy coefficient (W/GHz^3), uses default if None
            
        Returns:
            Computation energy in Joules
        """
        if kappa is None:
            kappa = self.device_config.kappa
        
        # Convert frequency to GHz for the formula
        frequency_ghz = frequency / 1e9
        
        # E_comp = κ * f^3 * T_local
        energy = kappa * (frequency_ghz ** 3) * computation_time
        
        return energy
    
    def calculate_communication_energy(self, data_size_bits: float, 
                                     transmission_rate: float, 
                                     tx_power: float = None) -> float:
        """
        Calculate communication energy consumption
        E_comm = P_tx * D_tx / R
        
        Args:
            data_size_bits: Data size in bits
            transmission_rate: Transmission rate in bps
            tx_power: Transmission power in Watts, uses random value if None
            
        Returns:
            Communication energy in Joules
        """
        if tx_power is None:
            tx_power = np.random.uniform(
                self.device_config.p_tx_min,
                self.device_config.p_tx_max
            )
        
        if transmission_rate <= 0:
            return 0.0
        
        # E_comm = P_tx * T_tx = P_tx * D_tx / R
        transmission_time = data_size_bits / transmission_rate
        energy = tx_power * transmission_time
        
        return energy
    
    def calculate_static_energy(self, total_time: float, static_power: float = None) -> float:
        """
        Calculate static energy consumption
        E_idle = P_static * T_total
        
        Args:
            total_time: Total time duration in seconds
            static_power: Static power in Watts, uses random value if None
            
        Returns:
            Static energy in Joules
        """
        if static_power is None:
            static_power = np.random.uniform(
                self.device_config.p_static_min,
                self.device_config.p_static_max
            )
        
        # E_idle = P_static * T_total
        energy = static_power * total_time
        
        return energy
    
    def calculate_total_device_energy(self, local_flops: float, device_frequency: float,
                                    g_device: float, data_size_bits: float,
                                    transmission_rate: float, total_time: float,
                                    kappa: float = None, tx_power: float = None,
                                    static_power: float = None) -> Dict[str, float]:
        """
        Calculate total energy consumption for a device
        E_total = E_comp + E_comm + E_idle
        
        Args:
            local_flops: FLOPs for local computation
            device_frequency: Device frequency in Hz
            g_device: Device computing capability (FLOPS per cycle)
            data_size_bits: Data size for transmission in bits
            transmission_rate: Transmission rate in bps
            total_time: Total time duration in seconds
            kappa: Energy coefficient (optional)
            tx_power: Transmission power (optional)
            static_power: Static power (optional)
            
        Returns:
            Dictionary with energy breakdown and total
        """
        # Calculate local computation time
        if local_flops > 0 and device_frequency > 0 and g_device > 0:
            computation_time = local_flops / (device_frequency * g_device)
        else:
            computation_time = 0.0
        
        # Calculate energy components
        comp_energy = self.calculate_computation_energy(
            device_frequency, computation_time, kappa
        )
        
        comm_energy = self.calculate_communication_energy(
            data_size_bits, transmission_rate, tx_power
        )
        
        static_energy = self.calculate_static_energy(
            total_time, static_power
        )
        
        total_energy = comp_energy + comm_energy + static_energy
        
        return {
            'computation_energy': comp_energy,
            'communication_energy': comm_energy,
            'static_energy': static_energy,
            'total_energy': total_energy,
            'computation_time': computation_time
        }
    
    def calculate_multi_device_energy(self, device_states: list, task_states: list,
                                    network_states: list) -> Dict[str, Any]:
        """
        Calculate energy consumption for multiple devices
        
        Args:
            device_states: List of device state dictionaries
            task_states: List of task state dictionaries
            network_states: List of network state dictionaries
            
        Returns:
            Dictionary with per-device and total energy consumption
        """
        device_energies = []
        total_system_energy = 0.0
        energy_breakdown = {
            'total_computation': 0.0,
            'total_communication': 0.0,
            'total_static': 0.0
        }
        
        for i, (device, task, network) in enumerate(zip(device_states, task_states, network_states)):
            # Extract parameters
            local_flops = task.get('local_flops', 0.0)
            device_frequency = device.get('frequency_hz', self.device_config.f_min)
            g_device = device.get('g_device', self.device_config.g_device)
            data_size_bits = task.get('transmission_data_size', 0)
            transmission_rate = network.get('transmission_rate', self.network_config.rate_min)
            total_time = task.get('total_time', config.optimization.delta_t)
            
            # Device-specific parameters
            kappa = device.get('kappa', self.device_config.kappa)
            tx_power = device.get('tx_power', None)
            static_power = device.get('static_power', None)
            
            # Calculate energy for this device
            energy_result = self.calculate_total_device_energy(
                local_flops, device_frequency, g_device, data_size_bits,
                transmission_rate, total_time, kappa, tx_power, static_power
            )
            
            device_energies.append(energy_result['total_energy'])
            total_system_energy += energy_result['total_energy']
            
            # Update breakdown
            energy_breakdown['total_computation'] += energy_result['computation_energy']
            energy_breakdown['total_communication'] += energy_result['communication_energy']
            energy_breakdown['total_static'] += energy_result['static_energy']
        
        return {
            'device_energies': device_energies,
            'total_system_energy': total_system_energy,
            'average_device_energy': total_system_energy / len(device_states) if device_states else 0.0,
            'energy_breakdown': energy_breakdown
        }
    
    def get_energy_efficiency_metrics(self, energy_consumption: float, 
                                    accuracy_achieved: float,
                                    inference_time: float) -> Dict[str, float]:
        """
        Calculate energy efficiency metrics
        
        Args:
            energy_consumption: Total energy consumption in Joules
            accuracy_achieved: Achieved inference accuracy (0-1)
            inference_time: Total inference time in seconds
            
        Returns:
            Dictionary with efficiency metrics
        """
        # Energy per accuracy point
        energy_per_accuracy = energy_consumption / accuracy_achieved if accuracy_achieved > 0 else float('inf')
        
        # Energy per second
        energy_per_second = energy_consumption / inference_time if inference_time > 0 else float('inf')
        
        # Energy-delay product
        energy_delay_product = energy_consumption * inference_time
        
        # Energy efficiency (accuracy per Joule)
        energy_efficiency = accuracy_achieved / energy_consumption if energy_consumption > 0 else 0.0
        
        return {
            'energy_per_accuracy': energy_per_accuracy,
            'energy_per_second': energy_per_second,
            'energy_delay_product': energy_delay_product,
            'energy_efficiency': energy_efficiency
        }
    
    def validate_energy_constraints(self, device_energies: list, 
                                  device_battery_levels: list) -> Dict[str, Any]:
        """
        Validate energy constraints for devices
        
        Args:
            device_energies: List of energy consumption values in Joules
            device_battery_levels: List of current battery levels in mAh
            
        Returns:
            Dictionary with constraint validation results
        """
        violations = []
        total_violations = 0
        
        for i, (energy_j, battery_mah) in enumerate(zip(device_energies, device_battery_levels)):
            # Convert energy to mAh for comparison
            energy_mah = energy_j / (self.device_config.voltage * 3.6)
            
            # Check if energy consumption exceeds available battery
            min_battery = self.device_config.min_battery_ratio * self.device_config.max_battery
            available_energy = battery_mah - min_battery
            
            if energy_mah > available_energy:
                violations.append({
                    'device_id': i,
                    'required_energy_mah': energy_mah,
                    'available_energy_mah': available_energy,
                    'violation_amount': energy_mah - available_energy
                })
                total_violations += 1
        
        return {
            'has_violations': len(violations) > 0,
            'violation_count': total_violations,
            'violations': violations,
            'violation_ratio': total_violations / len(device_energies) if device_energies else 0.0
        }

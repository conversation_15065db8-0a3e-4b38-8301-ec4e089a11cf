"""
ResNet-50 layer information extracted from resnet50_cifar_analysis.txt
Contains detailed layer specifications for partition point calculations
"""

from typing import List, Dict, Tu<PERSON>, NamedTuple

class LayerInfo(NamedTuple):
    """Information about a single layer"""
    layer_id: int
    layer_type: str
    input_shape: Tuple[int, int, int]
    output_shape: Tuple[int, int, int]
    flops: float
    params: int
    cumulative_flops: float
    has_early_exit: bool = False

# ResNet-50 layer information based on analysis file
RESNET50_LAYERS = [
    LayerInfo(0, "Conv2d", (3, 32, 32), (64, 32, 32), 1769472.0, 1728, 1769472.0),
    LayerInfo(1, "BatchNorm2d", (64, 32, 32), (64, 32, 32), 65536, 128, 1835008.0),
    LayerInfo(2, "ReLU", (64, 32, 32), (64, 32, 32), 0, 0, 1835008.0),
    LayerInfo(3, "Identity", (64, 32, 32), (64, 32, 32), 0, 0, 1835008.0),
    LayerInfo(4, "Conv2d", (64, 32, 32), (64, 32, 32), 4194304.0, 4096, 6029312.0),
    Layer<PERSON>nfo(5, "<PERSON>chNorm2d", (64, 32, 32), (64, 32, 32), 65536, 128, 6094848.0),
    LayerInfo(6, "ReLU", (64, 32, 32), (64, 32, 32), 0, 0, 6094848.0),
    LayerInfo(7, "Conv2d", (64, 32, 32), (64, 32, 32), 37748736.0, 36864, 43843584.0),
    LayerInfo(8, "BatchNorm2d", (64, 32, 32), (64, 32, 32), 65536, 128, 43909120.0),
    LayerInfo(9, "ReLU", (64, 32, 32), (64, 32, 32), 0, 0, 43909120.0),
    LayerInfo(10, "Conv2d", (64, 32, 32), (256, 32, 32), 16777216.0, 16384, 60686336.0),
    LayerInfo(11, "BatchNorm2d", (256, 32, 32), (256, 32, 32), 262144, 512, 60948480.0),
    LayerInfo(12, "Conv2d", (64, 32, 32), (256, 32, 32), 16777216.0, 16384, 77725696.0),
    LayerInfo(13, "BatchNorm2d", (256, 32, 32), (256, 32, 32), 262144, 512, 77987840.0),
    LayerInfo(14, "ReLU", (256, 32, 32), (256, 32, 32), 0, 0, 77987840.0),
    LayerInfo(15, "Conv2d", (256, 32, 32), (64, 32, 32), 16777216.0, 16384, 94765056.0),
    LayerInfo(16, "BatchNorm2d", (64, 32, 32), (64, 32, 32), 65536, 128, 94830592.0),
    LayerInfo(17, "ReLU", (64, 32, 32), (64, 32, 32), 0, 0, 94830592.0),
    LayerInfo(18, "Conv2d", (64, 32, 32), (64, 32, 32), 37748736.0, 36864, 132579328.0),
    LayerInfo(19, "BatchNorm2d", (64, 32, 32), (64, 32, 32), 65536, 128, 132644864.0),
    LayerInfo(20, "ReLU", (64, 32, 32), (64, 32, 32), 0, 0, 132644864.0),
    LayerInfo(21, "Conv2d", (64, 32, 32), (256, 32, 32), 16777216.0, 16384, 149422080.0),
    LayerInfo(22, "BatchNorm2d", (256, 32, 32), (256, 32, 32), 262144, 512, 149684224.0),
    LayerInfo(23, "ReLU", (256, 32, 32), (256, 32, 32), 0, 0, 149684224.0),
    LayerInfo(24, "Conv2d", (256, 32, 32), (64, 32, 32), 16777216.0, 16384, 166461440.0),
    LayerInfo(25, "BatchNorm2d", (64, 32, 32), (64, 32, 32), 65536, 128, 166526976.0),
    LayerInfo(26, "ReLU", (64, 32, 32), (64, 32, 32), 0, 0, 166526976.0),
    LayerInfo(27, "Conv2d", (64, 32, 32), (64, 32, 32), 37748736.0, 36864, 204275712.0),
    LayerInfo(28, "BatchNorm2d", (64, 32, 32), (64, 32, 32), 65536, 128, 204341248.0),
    LayerInfo(29, "ReLU", (64, 32, 32), (64, 32, 32), 0, 0, 204341248.0),
    LayerInfo(30, "Conv2d", (64, 32, 32), (256, 32, 32), 16777216.0, 16384, 221118464.0),
    LayerInfo(31, "BatchNorm2d", (256, 32, 32), (256, 32, 32), 262144, 512, 221380608.0),
    LayerInfo(32, "ReLU", (256, 32, 32), (256, 32, 32), 0, 0, 221380608.0),
    LayerInfo(33, "Conv2d", (256, 32, 32), (128, 32, 32), 33554432.0, 32768, 254935040.0),
    LayerInfo(34, "BatchNorm2d", (128, 32, 32), (128, 32, 32), 131072, 256, 255066112.0),
    LayerInfo(35, "ReLU", (128, 32, 32), (128, 32, 32), 0, 0, 255066112.0),
    LayerInfo(36, "Conv2d", (128, 32, 32), (128, 16, 16), 150994944.0, 147456, 406061056.0),
    LayerInfo(37, "BatchNorm2d", (128, 16, 16), (128, 16, 16), 32768, 256, 406093824.0),
    LayerInfo(38, "ReLU", (128, 16, 16), (128, 16, 16), 0, 0, 406093824.0),
    LayerInfo(39, "Conv2d", (128, 16, 16), (512, 16, 16), 16777216.0, 65536, 422871040.0),
    LayerInfo(40, "BatchNorm2d", (512, 16, 16), (512, 16, 16), 131072, 1024, 423002112.0),
    LayerInfo(41, "Conv2d", (256, 32, 32), (512, 16, 16), 134217728.0, 131072, 557219840.0),
    LayerInfo(42, "BatchNorm2d", (512, 16, 16), (512, 16, 16), 131072, 1024, 557350912.0),
    LayerInfo(43, "ReLU", (512, 16, 16), (512, 16, 16), 0, 0, 557350912.0),
    LayerInfo(44, "Conv2d", (512, 16, 16), (128, 16, 16), 16777216.0, 65536, 574128128.0),
    LayerInfo(45, "BatchNorm2d", (128, 16, 16), (128, 16, 16), 32768, 256, 574160896.0),
    LayerInfo(46, "ReLU", (128, 16, 16), (128, 16, 16), 0, 0, 574160896.0),
    LayerInfo(47, "Conv2d", (128, 16, 16), (128, 16, 16), 37748736.0, 147456, 611909632.0),
    LayerInfo(48, "BatchNorm2d", (128, 16, 16), (128, 16, 16), 32768, 256, 611942400.0),
    LayerInfo(49, "ReLU", (128, 16, 16), (128, 16, 16), 0, 0, 611942400.0),
    LayerInfo(50, "Conv2d", (128, 16, 16), (512, 16, 16), 16777216.0, 65536, 628719616.0),
    LayerInfo(51, "BatchNorm2d", (512, 16, 16), (512, 16, 16), 131072, 1024, 628850688.0),
    LayerInfo(52, "ReLU", (512, 16, 16), (512, 16, 16), 0, 0, 628850688.0),
    LayerInfo(53, "Conv2d", (512, 16, 16), (128, 16, 16), 16777216.0, 65536, 645627904.0),
    LayerInfo(54, "BatchNorm2d", (128, 16, 16), (128, 16, 16), 32768, 256, 645660672.0),
    LayerInfo(55, "ReLU", (128, 16, 16), (128, 16, 16), 0, 0, 645660672.0),
    LayerInfo(56, "Conv2d", (128, 16, 16), (128, 16, 16), 37748736.0, 147456, 683409408.0),
    LayerInfo(57, "BatchNorm2d", (128, 16, 16), (128, 16, 16), 32768, 256, 683442176.0),
    LayerInfo(58, "ReLU", (128, 16, 16), (128, 16, 16), 0, 0, 683442176.0),
    LayerInfo(59, "Conv2d", (128, 16, 16), (512, 16, 16), 16777216.0, 65536, 700219392.0),
    LayerInfo(60, "BatchNorm2d", (512, 16, 16), (512, 16, 16), 131072, 1024, 700350464.0),
    LayerInfo(61, "ReLU", (512, 16, 16), (512, 16, 16), 0, 0, 700350464.0),
    LayerInfo(62, "Conv2d", (512, 16, 16), (128, 16, 16), 16777216.0, 65536, 717127680.0),
    LayerInfo(63, "BatchNorm2d", (128, 16, 16), (128, 16, 16), 32768, 256, 717160448.0),
    LayerInfo(64, "ReLU", (128, 16, 16), (128, 16, 16), 0, 0, 717160448.0),
    LayerInfo(65, "Conv2d", (128, 16, 16), (128, 16, 16), 37748736.0, 147456, 754909184.0),
    LayerInfo(66, "BatchNorm2d", (128, 16, 16), (128, 16, 16), 32768, 256, 754941952.0),
    LayerInfo(67, "ReLU", (128, 16, 16), (128, 16, 16), 0, 0, 754941952.0),
    LayerInfo(68, "Conv2d", (128, 16, 16), (512, 16, 16), 16777216.0, 65536, 771719168.0),
    LayerInfo(69, "BatchNorm2d", (512, 16, 16), (512, 16, 16), 131072, 1024, 771850240.0),
    LayerInfo(70, "ReLU", (512, 16, 16), (512, 16, 16), 0, 0, 771850240.0),
    LayerInfo(71, "AdaptiveAvgPool2d", (512, 16, 16), (512, 1, 1), 0, 0, 771850240.0),
    LayerInfo(72, "Flatten", (512, 1, 1), (512,), 0, 0, 771850240.0),
    LayerInfo(73, "Linear", (512,), (10,), 5120, 5130, 771855360.0, True),  # Early Exit 1
    LayerInfo(74, "Conv2d", (512, 16, 16), (256, 16, 16), 33554432.0, 131072, 805409792.0),
    LayerInfo(75, "BatchNorm2d", (256, 16, 16), (256, 16, 16), 65536, 512, 805475328.0),
    LayerInfo(76, "ReLU", (256, 16, 16), (256, 16, 16), 0, 0, 805475328.0),
    LayerInfo(77, "Conv2d", (256, 16, 16), (256, 8, 8), 150994944.0, 589824, 956470272.0),
    LayerInfo(78, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 956486656.0),
    LayerInfo(79, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 956486656.0),
    LayerInfo(80, "Conv2d", (256, 8, 8), (1024, 8, 8), 16777216.0, 262144, 973263872.0),
    LayerInfo(81, "BatchNorm2d", (1024, 8, 8), (1024, 8, 8), 65536, 2048, 973329408.0),
    LayerInfo(82, "Conv2d", (512, 16, 16), (1024, 8, 8), 134217728.0, 524288, 1107547136.0),
    LayerInfo(83, "BatchNorm2d", (1024, 8, 8), (1024, 8, 8), 65536, 2048, 1107612672.0),
    LayerInfo(84, "ReLU", (1024, 8, 8), (1024, 8, 8), 0, 0, 1107612672.0),
    LayerInfo(85, "Conv2d", (1024, 8, 8), (256, 8, 8), 16777216.0, 262144, 1124389888.0),
    LayerInfo(86, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1124406272.0),
    LayerInfo(87, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1124406272.0),
    LayerInfo(88, "Conv2d", (256, 8, 8), (256, 8, 8), 37748736.0, 589824, 1162155008.0),
    LayerInfo(89, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1162171392.0),
    LayerInfo(90, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1162171392.0),
    LayerInfo(91, "Conv2d", (256, 8, 8), (1024, 8, 8), 16777216.0, 262144, 1178948608.0),
    LayerInfo(92, "BatchNorm2d", (1024, 8, 8), (1024, 8, 8), 65536, 2048, 1179014144.0),
    LayerInfo(93, "ReLU", (1024, 8, 8), (1024, 8, 8), 0, 0, 1179014144.0),
    LayerInfo(94, "Conv2d", (1024, 8, 8), (256, 8, 8), 16777216.0, 262144, 1195791360.0),
    LayerInfo(95, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1195807744.0),
    LayerInfo(96, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1195807744.0),
    LayerInfo(97, "Conv2d", (256, 8, 8), (256, 8, 8), 37748736.0, 589824, 1233556480.0),
    LayerInfo(98, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1233572864.0),
    LayerInfo(99, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1233572864.0),
    LayerInfo(100, "Conv2d", (256, 8, 8), (1024, 8, 8), 16777216.0, 262144, 1250350080.0),
    LayerInfo(101, "BatchNorm2d", (1024, 8, 8), (1024, 8, 8), 65536, 2048, 1250415616.0),
    LayerInfo(102, "ReLU", (1024, 8, 8), (1024, 8, 8), 0, 0, 1250415616.0),
    LayerInfo(103, "AdaptiveAvgPool2d", (1024, 8, 8), (1024, 1, 1), 0, 0, 1250415616.0),
    LayerInfo(104, "Flatten", (1024, 1, 1), (1024,), 0, 0, 1250415616.0),
    LayerInfo(105, "Linear", (1024,), (10,), 10240, 10250, 1250425856.0, True),  # Early Exit 2
    LayerInfo(106, "Conv2d", (1024, 8, 8), (256, 8, 8), 16777216.0, 262144, 1267203072.0),
    LayerInfo(107, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1267219456.0),
    LayerInfo(108, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1267219456.0),
    LayerInfo(109, "Conv2d", (256, 8, 8), (256, 8, 8), 37748736.0, 589824, 1304968192.0),
    LayerInfo(110, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1304984576.0),
    LayerInfo(111, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1304984576.0),
    LayerInfo(112, "Conv2d", (256, 8, 8), (1024, 8, 8), 16777216.0, 262144, 1321761792.0),
    LayerInfo(113, "BatchNorm2d", (1024, 8, 8), (1024, 8, 8), 65536, 2048, 1321827328.0),
    LayerInfo(114, "ReLU", (1024, 8, 8), (1024, 8, 8), 0, 0, 1321827328.0),
    LayerInfo(115, "Conv2d", (1024, 8, 8), (256, 8, 8), 16777216.0, 262144, 1338604544.0),
    LayerInfo(116, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1338620928.0),
    LayerInfo(117, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1338620928.0),
    LayerInfo(118, "Conv2d", (256, 8, 8), (256, 8, 8), 37748736.0, 589824, 1376369664.0),
    LayerInfo(119, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1376386048.0),
    LayerInfo(120, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1376386048.0),
    LayerInfo(121, "Conv2d", (256, 8, 8), (1024, 8, 8), 16777216.0, 262144, 1393163264.0),
    LayerInfo(122, "BatchNorm2d", (1024, 8, 8), (1024, 8, 8), 65536, 2048, 1393228800.0),
    LayerInfo(123, "ReLU", (1024, 8, 8), (1024, 8, 8), 0, 0, 1393228800.0),
    LayerInfo(124, "Conv2d", (1024, 8, 8), (256, 8, 8), 16777216.0, 262144, 1410006016.0),
    LayerInfo(125, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1410022400.0),
    LayerInfo(126, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1410022400.0),
    LayerInfo(127, "Conv2d", (256, 8, 8), (256, 8, 8), 37748736.0, 589824, 1447771136.0),
    LayerInfo(128, "BatchNorm2d", (256, 8, 8), (256, 8, 8), 16384, 512, 1447787520.0),
    LayerInfo(129, "ReLU", (256, 8, 8), (256, 8, 8), 0, 0, 1447787520.0),
    LayerInfo(130, "Conv2d", (256, 8, 8), (1024, 8, 8), 16777216.0, 262144, 1464564736.0),
    LayerInfo(131, "BatchNorm2d", (1024, 8, 8), (1024, 8, 8), 65536, 2048, 1464630272.0),
    LayerInfo(132, "ReLU", (1024, 8, 8), (1024, 8, 8), 0, 0, 1464630272.0),
    LayerInfo(133, "AdaptiveAvgPool2d", (1024, 8, 8), (1024, 1, 1), 0, 0, 1464630272.0),
    LayerInfo(134, "Flatten", (1024, 1, 1), (1024,), 0, 0, 1464630272.0),
    LayerInfo(135, "Linear", (1024,), (10,), 10240, 10250, 1464640512.0, True),  # Early Exit 3
    LayerInfo(136, "Conv2d", (1024, 8, 8), (512, 8, 8), 33554432.0, 524288, 1498194944.0),
    LayerInfo(137, "BatchNorm2d", (512, 8, 8), (512, 8, 8), 32768, 1024, 1498227712.0),
    LayerInfo(138, "ReLU", (512, 8, 8), (512, 8, 8), 0, 0, 1498227712.0),
    LayerInfo(139, "Conv2d", (512, 8, 8), (512, 4, 4), 150994944.0, 2359296, 1649222656.0),
    LayerInfo(140, "BatchNorm2d", (512, 4, 4), (512, 4, 4), 8192, 1024, 1649230848.0),
    LayerInfo(141, "ReLU", (512, 4, 4), (512, 4, 4), 0, 0, 1649230848.0),
    LayerInfo(142, "Conv2d", (512, 4, 4), (2048, 4, 4), 16777216.0, 1048576, 1666008064.0),
    LayerInfo(143, "BatchNorm2d", (2048, 4, 4), (2048, 4, 4), 32768, 4096, 1666040832.0),
    LayerInfo(144, "Conv2d", (1024, 8, 8), (2048, 4, 4), 134217728.0, 2097152, 1800258560.0),
    LayerInfo(145, "BatchNorm2d", (2048, 4, 4), (2048, 4, 4), 32768, 4096, 1800291328.0),
    LayerInfo(146, "ReLU", (2048, 4, 4), (2048, 4, 4), 0, 0, 1800291328.0),
    LayerInfo(147, "Conv2d", (2048, 4, 4), (512, 4, 4), 16777216.0, 1048576, 1817068544.0),
    LayerInfo(148, "BatchNorm2d", (512, 4, 4), (512, 4, 4), 8192, 1024, 1817076736.0),
    LayerInfo(149, "ReLU", (512, 4, 4), (512, 4, 4), 0, 0, 1817076736.0),
    LayerInfo(150, "Conv2d", (512, 4, 4), (512, 4, 4), 37748736.0, 2359296, 1854825472.0),
    LayerInfo(151, "BatchNorm2d", (512, 4, 4), (512, 4, 4), 8192, 1024, 1854833664.0),
    LayerInfo(152, "ReLU", (512, 4, 4), (512, 4, 4), 0, 0, 1854833664.0),
    LayerInfo(153, "Conv2d", (512, 4, 4), (2048, 4, 4), 16777216.0, 1048576, 1871610880.0),
    LayerInfo(154, "BatchNorm2d", (2048, 4, 4), (2048, 4, 4), 32768, 4096, 1871643648.0),
    LayerInfo(155, "ReLU", (2048, 4, 4), (2048, 4, 4), 0, 0, 1871643648.0),
    LayerInfo(156, "Conv2d", (2048, 4, 4), (512, 4, 4), 16777216.0, 1048576, 1888420864.0),
    LayerInfo(157, "BatchNorm2d", (512, 4, 4), (512, 4, 4), 8192, 1024, 1888429056.0),
    LayerInfo(158, "ReLU", (512, 4, 4), (512, 4, 4), 0, 0, 1888429056.0),
    LayerInfo(159, "Conv2d", (512, 4, 4), (512, 4, 4), 37748736.0, 2359296, 1926177792.0),
    LayerInfo(160, "BatchNorm2d", (512, 4, 4), (512, 4, 4), 8192, 1024, 1926185984.0),
    LayerInfo(161, "ReLU", (512, 4, 4), (512, 4, 4), 0, 0, 1926185984.0),
    LayerInfo(162, "Conv2d", (512, 4, 4), (2048, 4, 4), 16777216.0, 1048576, 1942963200.0),
    LayerInfo(163, "BatchNorm2d", (2048, 4, 4), (2048, 4, 4), 32768, 4096, 1942995968.0),
    LayerInfo(164, "ReLU", (2048, 4, 4), (2048, 4, 4), 0, 0, 1942995968.0),
    LayerInfo(165, "AdaptiveAvgPool2d", (2048, 4, 4), (2048, 1, 1), 0, 0, 1942995968.0),
    LayerInfo(166, "Linear", (2048,), (10,), 20480, 20490, 1943016448.0, True),  # Early Exit 4 & 5 & Full
]

def get_layer_info(layer_id: int) -> LayerInfo:
    """Get layer information by layer ID"""
    if 0 <= layer_id < len(RESNET50_LAYERS):
        return RESNET50_LAYERS[layer_id]
    raise ValueError(f"Invalid layer ID: {layer_id}")

def get_conv_layers() -> List[int]:
    """Get all convolutional layer indices"""
    return [i for i, layer in enumerate(RESNET50_LAYERS) if layer.layer_type == "Conv2d"]

def get_early_exit_layers() -> List[int]:
    """Get all early exit layer indices"""
    return [i for i, layer in enumerate(RESNET50_LAYERS) if layer.has_early_exit]

def calculate_flops_up_to_layer(layer_id: int) -> float:
    """Calculate cumulative FLOPs up to specified layer"""
    if 0 <= layer_id < len(RESNET50_LAYERS):
        return RESNET50_LAYERS[layer_id].cumulative_flops
    return 0.0

def get_output_size_at_layer(layer_id: int) -> int:
    """Get output data size at specified layer (in bits)"""
    if 0 <= layer_id < len(RESNET50_LAYERS):
        layer = RESNET50_LAYERS[layer_id]
        if len(layer.output_shape) == 3:  # (C, H, W)
            c, h, w = layer.output_shape
            return c * h * w * 32  # FP32 = 32 bits
        elif len(layer.output_shape) == 1:  # (features,)
            return layer.output_shape[0] * 32
    return 0

# 异构多边缘服务器DNN推理优化框架详细技术文档

## 1. 项目概述

### 1.1 项目结构
```
├── config.py                 # 全局配置参数
├── resnet_layer_info.py      # ResNet-50层级规格信息
├── models/                   # 核心系统模型
│   ├── device.py            # LocalDevice类 - 本地设备模型
│   ├── edge_server.py       # EdgeServer类 - 边缘服务器模型
│   └── inference_task.py    # EdgeInferenceTask类 - 推理任务模型
├── performance/             # 性能模型
│   ├── energy_model.py      # 能耗计算模型
│   ├── delay_model.py       # 延迟计算模型
│   └── adaptive_weight.py   # 电池感知权重机制
├── networks/                # 神经网络实现
│   ├── resnet50_early_exit.py # 带早期退出的ResNet-50
│   └── maddpg.py           # MADDPG算法实现
├── environment/             # 强化学习环境
│   └── edge_env.py         # 多智能体边缘计算环境
├── utils/                   # 工具函数
│   └── visualization.py    # 绘图和分析工具
├── baselines.py            # 基线算法实现
├── main.py                 # 训练和评估主脚本
└── analyze_results.py      # 结果分析脚本
```

### 1.2 核心技术栈
- **深度学习框架**: PyTorch >= 1.12.0, TorchVision >= 0.13.0
- **数值计算**: NumPy >= 1.21.0, SciPy >= 1.7.0, Pandas >= 1.3.0
- **可视化**: Matplotlib >= 3.5.0, Seaborn >= 0.11.0, Plotly >= 5.0.0
- **机器学习**: Scikit-learn >= 1.0.0
- **工具库**: tqdm >= 4.62.0, PyYAML >= 6.0

## 2. 配置系统详细分析 (config.py)

### 2.1 设备配置 (DeviceConfig)
```python
@dataclass
class DeviceConfig:
    # 频率参数 (Hz)
    f_device_min: float = 1.2e9    # 最小频率: 1.2 GHz
    f_device_max: float = 1.8e9    # 最大频率: 1.8 GHz

    # 计算能力
    g_device: float = 153.0        # 每周期FLOPS数

    # 电池参数
    battery_capacity: float = 4020.0  # 电池容量: 4020 mAh
    initial_battery_min: float = 0.6  # 初始电池最小值: 60%
    initial_battery_max: float = 0.9  # 初始电池最大值: 90%

    # 功耗参数 (W)
    p_static_min: float = 0.66     # 静态功耗最小值
    p_static_max: float = 0.82     # 静态功耗最大值
    p_tx_min: float = 0.093        # 传输功耗最小值
    p_tx_max: float = 0.115        # 传输功耗最大值
    kappa_min: float = 1e-28       # 能耗系数最小值
    kappa_max: float = 1.5e-28     # 能耗系数最大值

    # 设备类型
    device_type: str = "jetson_xavier"
```

**关键参数说明**:
- `g_device = 153.0`: 每个时钟周期可执行153个浮点运算
- `kappa`: 动态功耗系数，用于计算 P_comp = kappa × f³
- 电池容量基于NVIDIA Jetson Xavier的实际规格

### 2.2 服务器配置 (ServerConfig)
```python
@dataclass
class ServerConfig:
    # 服务器频率参数 (Hz)
    f_server_min: float = 2.2e9    # 最小频率: 2.2 GHz
    f_server_max: float = 2.8e9    # 最大频率: 2.8 GHz

    # 计算能力
    g_server: float = 380.0        # 每周期FLOPS数

    # 负载参数
    max_load: float = 0.75         # 最大负载比率: 75%

    # 服务器规格
    num_servers: int = 4           # 服务器数量
    server_type: str = "laptop_4060"
```

**性能对比分析**:
- 服务器计算能力 (380 FLOPS/cycle) vs 设备计算能力 (153 FLOPS/cycle)
- 性能提升比例: 380/153 ≈ 2.48倍
- 频率优势: 2.2-2.8 GHz vs 1.2-1.8 GHz

### 2.3 网络配置 (NetworkConfig)
```python
@dataclass
class NetworkConfig:
    # 传输速率参数 (bps)
    rate_min: float = 15.5e6       # 最小传输速率: 15.5 Mbps
    rate_max: float = 25.0e6       # 最大传输速率: 25.0 Mbps

    # 数据传输参数
    bits_per_float: int = 32       # FP32模型，每个浮点数32位
```

**网络性能分析**:
- 传输速率范围: 15.5-25 Mbps (相对较低的边缘网络条件)
- 数据精度: 32位浮点数 (标准深度学习精度)

### 2.4 ResNet配置 (ResNetConfig)
```python
@dataclass
class ResNetConfig:
    # 早期退出准确率
    early_exit_accuracies = {
        "Exit_1": 0.7700,  # 第73层退出，准确率77.00%
        "Exit_2": 0.8731,  # 第105层退出，准确率87.31%
        "Exit_3": 0.9038,  # 第135层退出，准确率90.38%
        "Exit_4": 0.9156,  # 第150层退出，准确率91.56%
        "Exit_5": 0.9203,  # 第160层退出，准确率92.03%
        "Full": 0.9240     # 完整模型，准确率92.40%
    }

    # 早期退出层级位置
    early_exit_layers = {
        "Exit_1": 73,   "Exit_2": 105,  "Exit_3": 135,
        "Exit_4": 150,  "Exit_5": 160,  "Full": 162
    }

    # 有效分割点 (Conv2d层索引)
    valid_partition_points = [
        0, 4, 7, 10, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 41, 44, 47, 50,
        53, 56, 59, 62, 65, 68, 74, 77, 80, 82, 85, 88, 91, 94, 97, 100,
        106, 109, 112, 115, 118, 121, 124, 127, 130, 136, 139, 142, 144,
        147, 150, 153, 156, 159, 162
    ]
```

**早期退出策略分析**:
- Exit_1 (77%): 适用于对准确率要求不高的任务
- Exit_2 (87.31%): 平衡准确率和计算量的选择
- Exit_3 (90.38%): 高准确率要求的标准选择
- 准确率递增幅度: Exit_1→Exit_2 (+10.31%), Exit_2→Exit_3 (+3.07%)

### 2.5 优化配置 (OptimizationConfig)
```python
@dataclass
class OptimizationConfig:
    # 准确率约束
    acc_min_range: Tuple[float, float] = (0.7, 0.91)  # 准确率要求范围
    default_acc_min: float = 0.85                     # 默认最小准确率

    # 延迟约束
    t_max: float = 0.05            # 最大延迟: 50ms

    # 自适应权重参数
    beta: float = 10.0             # 敏感度参数
    theta: float = 0.4             # 阈值参数

    # 全局时间槽
    delta_t: float = 1.0           # 时间槽: 1秒
```

**约束条件分析**:
- 延迟约束 (50ms): 实时推理应用的严格要求
- 准确率范围 (70%-91%): 覆盖不同应用场景的需求
- 自适应权重机制: 基于电池状态动态调整优化目标

### 2.6 MADDPG配置 (MADDPGConfig)
```python
@dataclass
class MADDPGConfig:
    # 网络架构
    state_dim: int = 2             # 状态维度: [电池比率, 频率比率]
    action_dim: int = 3            # 动作维度: [分割点, 服务器选择, 频率]

    # 学习参数
    lr_actor: float = 1e-4         # Actor学习率
    lr_critic: float = 1e-3        # Critic学习率
    gamma: float = 0.95            # 折扣因子
    tau: float = 0.01              # 软更新参数

    # 经验回放
    buffer_size: int = 100000      # 经验池大小
    batch_size: int = 64           # 批次大小

    # 探索策略
    noise_std: float = 0.2         # 噪声标准差
    noise_decay: float = 0.995     # 噪声衰减率
    min_noise: float = 0.01        # 最小噪声

    # 梯度裁剪
    grad_clip: float = 1.0         # 梯度裁剪阈值

    # 网络隐藏层维度
    actor_hidden_dims: List[int] = [128, 64]
    critic_hidden_dims: List[int] = [128, 64]
```

### 2.7 环境配置 (EnvironmentConfig)
```python
@dataclass
class EnvironmentConfig:
    # 智能体数量
    num_devices: int = 4           # 设备数量 (智能体数量)

    # 仿真参数
    max_steps: int = 100           # 每个episode最大步数

    # 奖励缩放因子
    max_delay_normalization: float = 0.05    # 最大延迟归一化: 50ms
    max_energy_normalization: float = 72.0   # 最大设备能耗归一化

    # 惩罚值
    battery_depletion_penalty: float = 100.0      # 电池耗尽惩罚
    delay_violation_penalty: float = 50.0         # 延迟违约惩罚
    accuracy_violation_penalty: float = 200.0     # 准确率违约惩罚
    server_overload_penalty: float = 30.0         # 服务器过载惩罚
```

## 3. 核心模型详细分析 (models/)

### 3.1 本地设备模型 (LocalDevice)

#### 3.1.1 设备初始化
```python
class LocalDevice:
    def __init__(self, device_id: int, initial_battery_ratio: float = 1.0):
        # 设备标识
        self.device_id = device_id

        # 电池状态管理
        self.max_battery = config.device.battery_capacity      # 4020 mAh
        self.current_battery = initial_battery_ratio * self.max_battery
        self.min_battery = 0.1 * self.max_battery             # 10%最小电量
        self.voltage = 3.7                                    # 3.7V标准电压

        # 频率控制
        self.current_frequency = config.device.f_device_min   # 1.2 GHz
        self.f_min = config.device.f_device_min               # 1.2 GHz
        self.f_max = config.device.f_device_max               # 1.8 GHz

        # 计算能力
        self.g_device = config.device.g_device                # 153 FLOPS/cycle

        # 能耗参数
        self.kappa = random.uniform(1e-28, 1.5e-28)          # 动态功耗系数
        self.p_static = random.uniform(0.66, 0.82)           # 静态功耗 (W)
        self.p_tx = random.uniform(0.093, 0.115)             # 传输功耗 (W)
```

#### 3.1.2 状态获取方法
```python
def get_state(self) -> np.ndarray:
    """获取RL智能体的状态向量"""
    battery_ratio = self.current_battery / self.max_battery
    frequency_ratio = (self.current_frequency - self.f_min) / (self.f_max - self.f_min)
    return np.array([battery_ratio, frequency_ratio], dtype=np.float32)
```

**状态空间分析**:
- **维度**: 2维连续状态空间
- **battery_ratio**: [0, 1] - 当前电池电量比率
- **frequency_ratio**: [0, 1] - 当前频率相对于频率范围的比率

#### 3.1.3 频率控制方法
```python
def set_frequency(self, frequency: float) -> bool:
    """设置设备工作频率"""
    if self.f_min <= frequency <= self.f_max:
        self.current_frequency = frequency
        self.frequency_history.append(frequency)
        return True
    return False

def set_frequency_ratio(self, ratio: float) -> bool:
    """通过比率设置频率 (0-1映射到f_min-f_max)"""
    ratio = np.clip(ratio, 0.0, 1.0)
    target_frequency = self.f_min + ratio * (self.f_max - self.f_min)
    return self.set_frequency(target_frequency)
```

#### 3.1.4 自适应权重机制
```python
def get_adaptive_weight(self) -> float:
    """计算基于电池状态的自适应权重"""
    battery_ratio = self.get_battery_ratio()
    beta = config.optimization.beta      # 10.0 (敏感度参数)
    theta = config.optimization.theta    # 0.4 (阈值参数)

    if battery_ratio >= theta:
        # 电池充足时，权重为0.5 (平衡延迟和能耗)
        return 0.5
    else:
        # 电池不足时，增加能耗权重
        return 0.5 + 0.5 * (1 - np.exp(-beta * (theta - battery_ratio)))
```

**自适应权重机制分析**:
- **阈值θ = 0.4**: 当电池低于40%时触发能耗优先模式
- **敏感度β = 10.0**: 控制权重变化的陡峭程度
- **权重范围**: [0.5, 1.0] - 从平衡模式到能耗优先模式

#### 3.1.5 能耗管理方法
```python
def consume_energy(self, energy_joules: float) -> bool:
    """消耗电池能量"""
    # 将焦耳转换为mAh: Energy(J) / Voltage(V) / 3.6 = mAh
    energy_mah = energy_joules / (self.voltage * 3.6)

    if self.current_battery - energy_mah >= self.min_battery:
        self.current_battery -= energy_mah
        self.energy_history.append(energy_joules)
        return True
    else:
        # 电池耗尽
        self.current_battery = max(0, self.current_battery - energy_mah)
        return False

def is_depleted(self) -> bool:
    """检查电池是否耗尽"""
    return self.current_battery <= self.min_battery
```

### 3.2 边缘服务器模型 (EdgeServer)

#### 3.2.1 服务器初始化
```python
class EdgeServer:
    def __init__(self, server_id: int):
        # 服务器标识
        self.server_id = server_id

        # 服务器规格
        self.f_server = random.uniform(2.2e9, 2.8e9)         # 2.2-2.8 GHz
        self.g_server = config.server.g_server               # 380 FLOPS/cycle
        self.max_load = config.server.max_load               # 0.75 (75%最大负载)
        self.server_type = "laptop_4060"

        # 当前状态
        self.current_load = 0.0                              # 当前负载比率
        self.active_tasks = []                               # 活跃任务列表
        self.total_flops_allocated = 0.0                     # 已分配的总FLOPS
```

#### 3.2.2 容量管理方法
```python
def get_max_capacity(self) -> float:
    """获取最大计算容量 (FLOPS)"""
    delta_t = config.optimization.delta_t  # 1.0秒时间槽
    return self.f_server * self.g_server * self.max_load * delta_t

def get_available_capacity(self) -> float:
    """获取可用计算容量"""
    return self.get_max_capacity() - self.total_flops_allocated

def can_accept_task(self, required_flops: float) -> bool:
    """检查是否可以接受新任务"""
    return required_flops <= self.get_available_capacity()
```

**容量计算公式**:
- **最大容量** = 频率 × 每周期FLOPS × 最大负载比率 × 时间槽
- **可用容量** = 最大容量 - 已分配容量
- **典型最大容量**: 2.5GHz × 380 FLOPS/cycle × 0.75 × 1s ≈ 7.125 × 10¹¹ FLOPS

#### 3.2.3 任务分配方法
```python
def allocate_task(self, task_id: int, required_flops: float, device_id: int) -> bool:
    """为任务分配计算资源"""
    if self.can_accept_task(required_flops):
        task_info = {
            'task_id': task_id,
            'device_id': device_id,
            'required_flops': required_flops,
            'allocated_time': 0.0
        }
        self.active_tasks.append(task_info)
        self.total_flops_allocated += required_flops
        self._update_load()
        return True
    return False

def _update_load(self):
    """更新当前负载比率"""
    max_capacity = self.get_max_capacity()
    if max_capacity > 0:
        self.current_load = self.total_flops_allocated / max_capacity
    else:
        self.current_load = 0.0
```

#### 3.2.4 负载管理和惩罚机制
```python
def is_overloaded(self) -> bool:
    """检查服务器是否过载"""
    return self.current_load > self.max_load

def get_load_penalty(self) -> float:
    """计算负载惩罚"""
    if self.is_overloaded():
        overload_ratio = self.current_load - self.max_load
        return overload_ratio * 100.0  # 线性惩罚
    return 0.0

def get_current_utilization(self) -> float:
    """获取当前利用率"""
    return min(self.current_load, 1.0)

### 3.3 推理任务模型 (EdgeInferenceTask)

#### 3.3.1 任务初始化
```python
class EdgeInferenceTask:
    def __init__(self, task_id: int, accuracy_requirement: float = None):
        # 任务标识
        self.task_id = task_id

        # 准确率要求
        if accuracy_requirement is None:
            self.accuracy_requirement = random.uniform(
                config.optimization.acc_min_range[0],  # 0.7
                config.optimization.acc_min_range[1]   # 0.91
            )
        else:
            self.accuracy_requirement = accuracy_requirement

        # 基于准确率要求选择早期退出点
        self.early_exit_name = config.get_early_exit_for_accuracy(self.accuracy_requirement)
        self.early_exit_point = config.resnet.early_exit_layers[self.early_exit_name]
        self.achieved_accuracy = config.resnet.early_exit_accuracies[self.early_exit_name]

        # 有效分割点
        self.valid_partition_points = config.resnet.valid_partition_points

        # 决策变量
        self.partition_point = 0          # 0表示完全本地执行
        self.target_server = None         # None表示本地执行
        self.device_frequency = config.device.f_device_min
```

**早期退出选择逻辑**:
- 根据准确率要求自动选择最低满足条件的退出点
- 例如: 要求85%准确率 → 选择Exit_2 (87.31%)
- 避免过度计算，提高效率

#### 3.3.2 FLOPS计算方法
```python
def get_local_flops(self) -> float:
    """获取本地计算的FLOPS"""
    if self.partition_point == 0:
        # 完全本地执行 - 计算到早期退出点
        return calculate_flops_up_to_layer(self.early_exit_point)
    else:
        # 部分卸载 - 计算到分割点
        return calculate_flops_up_to_layer(self.partition_point)

def get_edge_flops(self) -> float:
    """获取边缘计算的FLOPS"""
    if self.partition_point == 0:
        # 完全本地执行 - 无边缘计算
        return 0.0
    elif self.partition_point == self.early_exit_point + 1:
        # 完全卸载 - 所有计算在边缘
        return calculate_flops_up_to_layer(self.early_exit_point)
    else:
        # 部分卸载 - 从分割点到早期退出点
        local_flops = calculate_flops_up_to_layer(self.partition_point)
        total_flops = calculate_flops_up_to_layer(self.early_exit_point)
        return total_flops - local_flops
```

**FLOPS分配策略**:
- **partition_point = 0**: 完全本地执行
- **partition_point = early_exit_point + 1**: 完全边缘执行
- **其他值**: 部分卸载，在分割点处传输中间特征

#### 3.3.3 数据传输大小计算
```python
def get_transmission_data_size(self) -> float:
    """获取传输数据大小 (bits)"""
    if self.partition_point == 0:
        # 完全本地执行 - 无数据传输
        return 0.0

    # 获取分割点的输出特征图大小
    output_size = get_output_size_at_layer(self.partition_point)

    # 转换为bits (FP32: 32 bits per float)
    bits_per_float = config.network.bits_per_float  # 32
    return output_size * bits_per_float
```

#### 3.3.4 延迟计算方法
```python
def calculate_local_delay(self, device_frequency: float, g_device: float) -> float:
    """计算本地处理延迟"""
    local_flops = self.get_local_flops()
    if local_flops > 0:
        return local_flops / (device_frequency * g_device)
    return 0.0

def calculate_communication_delay(self, transmission_rate: float) -> float:
    """计算通信传输延迟"""
    data_size = self.get_transmission_data_size()
    if data_size > 0:
        return data_size / transmission_rate
    return 0.0

def calculate_edge_delay(self, server_frequency: float, g_server: float) -> float:
    """计算边缘处理延迟"""
    edge_flops = self.get_edge_flops()
    if edge_flops > 0:
        return edge_flops / (server_frequency * g_server)
    return 0.0
```

#### 3.3.5 约束检查方法
```python
def is_accuracy_satisfied(self) -> bool:
    """检查准确率约束是否满足"""
    return self.achieved_accuracy >= self.accuracy_requirement

def get_accuracy_violation_penalty(self) -> float:
    """计算准确率违约惩罚"""
    if not self.is_accuracy_satisfied():
        violation = self.accuracy_requirement - self.achieved_accuracy
        return violation * config.environment.accuracy_violation_penalty  # 200.0
    return 0.0

def is_delay_satisfied(self, total_delay: float) -> bool:
    """检查延迟约束是否满足"""
    return total_delay <= config.optimization.t_max  # 0.05s (50ms)

def get_delay_violation_penalty(self, total_delay: float) -> float:
    """计算延迟违约惩罚"""
    if not self.is_delay_satisfied(total_delay):
        violation = total_delay - config.optimization.t_max
        return violation * config.environment.delay_violation_penalty  # 50.0
    return 0.0
```

## 4. 性能模型详细分析 (performance/)

### 4.1 能耗模型 (EnergyModel)

#### 4.1.1 计算能耗公式
```python
def calculate_computation_energy(self, frequency: float, computation_time: float,
                               kappa: float = None) -> float:
    """
    计算计算能耗
    E_comp = κ × f³ × T_local

    Args:
        frequency: GPU频率 (Hz)
        computation_time: 本地计算时间 (秒)
        kappa: 能耗系数 (W/GHz³)

    Returns:
        计算能耗 (焦耳)
    """
    if kappa is None:
        kappa = config.device.kappa  # 1e-28 到 1.5e-28

    # 将频率转换为GHz
    frequency_ghz = frequency / 1e9

    # E_comp = κ × f³ × T_local
    energy = kappa * (frequency_ghz ** 3) * computation_time

    return energy
```

**能耗公式分析**:
- **立方关系**: 频率与能耗呈立方关系，频率翻倍能耗增加8倍
- **κ系数**: 1e-28 到 1.5e-28 W/GHz³，基于实际硬件测量
- **典型值**: 1.5GHz × 0.1s → 约 3.375e-28 × (1.5)³ × 0.1 ≈ 1.14e-28 J

#### 4.1.2 通信能耗公式
```python
def calculate_communication_energy(self, data_size_bits: float,
                                 transmission_rate: float,
                                 tx_power: float = None) -> float:
    """
    计算通信能耗
    E_comm = P_tx × T_tx = P_tx × D_tx / R

    Args:
        data_size_bits: 数据大小 (bits)
        transmission_rate: 传输速率 (bps)
        tx_power: 传输功率 (W)

    Returns:
        通信能耗 (焦耳)
    """
    if tx_power is None:
        tx_power = random.uniform(0.093, 0.115)  # W

    if transmission_rate <= 0:
        return 0.0

    # E_comm = P_tx × T_tx = P_tx × D_tx / R
    transmission_time = data_size_bits / transmission_rate
    energy = tx_power * transmission_time

    return energy
```

**通信能耗分析**:
- **传输时间**: T_tx = 数据大小 / 传输速率
- **功率范围**: 0.093-0.115 W (基于WiFi/4G实测)
- **典型值**: 1MB数据，20Mbps → 0.4s × 0.1W = 0.04J

#### 4.1.3 静态能耗公式
```python
def calculate_static_energy(self, total_time: float, static_power: float = None) -> float:
    """
    计算静态能耗
    E_static = P_static × T_total

    Args:
        total_time: 总时间 (秒)
        static_power: 静态功率 (W)

    Returns:
        静态能耗 (焦耳)
    """
    if static_power is None:
        static_power = random.uniform(0.66, 0.82)  # W

    # E_static = P_static × T_total
    energy = static_power * total_time

    return energy
```

#### 4.1.4 总能耗计算
```python
def calculate_total_device_energy(self, local_flops: float, device_frequency: float,
                                g_device: float, data_size_bits: float,
                                transmission_rate: float, total_time: float) -> Dict[str, float]:
    """
    计算设备总能耗
    E_total = E_comp + E_comm + E_static
    """
    # 计算本地计算时间
    if local_flops > 0:
        computation_time = local_flops / (device_frequency * g_device)
    else:
        computation_time = 0.0

    # 计算各部分能耗
    comp_energy = self.calculate_computation_energy(device_frequency, computation_time)
    comm_energy = self.calculate_communication_energy(data_size_bits, transmission_rate)
    static_energy = self.calculate_static_energy(total_time)

    total_energy = comp_energy + comm_energy + static_energy

    return {
        'computation_energy': comp_energy,
        'communication_energy': comm_energy,
        'static_energy': static_energy,
        'total_energy': total_energy
    }

### 4.2 延迟模型 (DelayModel)

#### 4.2.1 本地计算延迟公式
```python
def calculate_local_delay(self, local_flops: float, device_frequency: float,
                        g_device: float) -> float:
    """
    计算本地处理延迟
    T_local = C_local / (f_device × g_device)

    Args:
        local_flops: 本地计算FLOPS数
        device_frequency: 设备频率 (Hz)
        g_device: 设备计算能力 (FLOPS/cycle)

    Returns:
        本地处理延迟 (秒)
    """
    if local_flops <= 0 or device_frequency <= 0 or g_device <= 0:
        return 0.0

    # T_local = C_local / (f_device × g_device)
    delay = local_flops / (device_frequency * g_device)

    return delay
```

**本地延迟分析**:
- **计算公式**: 延迟 = FLOPS / (频率 × 每周期FLOPS)
- **典型值**: 1e9 FLOPS / (1.5e9 Hz × 153 FLOPS/cycle) ≈ 4.36ms
- **频率影响**: 频率提高直接降低延迟

#### 4.2.2 通信延迟公式
```python
def calculate_communication_delay(self, data_size_bits: float,
                                transmission_rate: float) -> float:
    """
    计算通信传输延迟
    T_comm = D_tx / R_tx

    Args:
        data_size_bits: 数据大小 (bits)
        transmission_rate: 传输速率 (bps)

    Returns:
        通信延迟 (秒)
    """
    if data_size_bits <= 0 or transmission_rate <= 0:
        return 0.0

    # T_comm = D_tx / R_tx
    delay = data_size_bits / transmission_rate

    return delay
```

**通信延迟分析**:
- **传输公式**: 延迟 = 数据大小 / 传输速率
- **典型值**: 8MB数据 / 20Mbps = 3.2s (相对较大)
- **网络瓶颈**: 在低带宽环境下通信延迟占主导

#### 4.2.3 边缘计算延迟公式
```python
def calculate_edge_delay(self, edge_flops: float, server_frequency: float,
                       g_server: float) -> float:
    """
    计算边缘处理延迟
    T_edge = C_edge / (f_server × g_server)

    Args:
        edge_flops: 边缘计算FLOPS数
        server_frequency: 服务器频率 (Hz)
        g_server: 服务器计算能力 (FLOPS/cycle)

    Returns:
        边缘处理延迟 (秒)
    """
    if edge_flops <= 0 or server_frequency <= 0 or g_server <= 0:
        return 0.0

    # T_edge = C_edge / (f_server × g_server)
    delay = edge_flops / (server_frequency * g_server)

    return delay
```

**边缘延迟分析**:
- **性能优势**: 服务器频率(2.2-2.8GHz) > 设备频率(1.2-1.8GHz)
- **计算能力**: 服务器(380 FLOPS/cycle) > 设备(153 FLOPS/cycle)
- **综合优势**: 约2.48倍的计算性能提升

#### 4.2.4 总延迟计算
```python
def calculate_total_delay(self, local_flops: float, edge_flops: float,
                        data_size_bits: float, device_frequency: float,
                        server_frequency: float, transmission_rate: float) -> Dict[str, float]:
    """
    计算端到端总延迟
    T_total = T_local + T_comm + T_edge
    """
    # 计算各部分延迟
    local_delay = self.calculate_local_delay(local_flops, device_frequency, g_device)
    comm_delay = self.calculate_communication_delay(data_size_bits, transmission_rate)
    edge_delay = self.calculate_edge_delay(edge_flops, server_frequency, g_server)

    total_delay = local_delay + comm_delay + edge_delay

    return {
        'local_delay': local_delay,
        'communication_delay': comm_delay,
        'edge_delay': edge_delay,
        'total_delay': total_delay
    }
```

### 4.3 自适应权重机制 (AdaptiveWeightMechanism)

#### 4.3.1 权重计算公式
```python
def calculate_weight(self, battery_ratio: float) -> float:
    """
    基于电池水平计算自适应权重
    α_m = 1 / (1 + exp(-β(B_m/B_max - θ)))

    Args:
        battery_ratio: 当前电池水平比率 (0-1)

    Returns:
        自适应权重 (0-1) 用于能耗-延迟权衡
    """
    # 限制电池比率到有效范围
    battery_ratio = max(0.0, min(1.0, battery_ratio))

    # α_m = 1 / (1 + exp(-β(B_m/B_max - θ)))
    exponent = -self.beta * (battery_ratio - self.theta)
    weight = 1.0 / (1.0 + np.exp(exponent))

    return weight
```

**权重机制分析**:
- **Sigmoid函数**: 平滑的权重过渡
- **β = 10.0**: 控制过渡的陡峭程度
- **θ = 0.4**: 权重为0.5时的电池阈值
- **权重含义**:
  - 接近1.0: 优先考虑延迟 (电池充足)
  - 接近0.0: 优先考虑能耗 (电池不足)

#### 4.3.2 目标函数计算
```python
def calculate_objective_function(self, delay: float, energy: float,
                               weight: float, max_delay: float = None,
                               max_energy: float = None) -> float:
    """
    计算加权目标函数值
    J_m = α_m × T_m + (1-α_m) × E_m

    Args:
        delay: 延迟值 (秒)
        energy: 能耗值 (焦耳)
        weight: 自适应权重 (0-1)
        max_delay: 延迟归一化最大值
        max_energy: 能耗归一化最大值

    Returns:
        加权目标函数值
    """
    # 归一化处理
    if max_delay is not None and max_delay > 0:
        normalized_delay = delay / max_delay
    else:
        normalized_delay = delay

    if max_energy is not None and max_energy > 0:
        normalized_energy = energy / max_energy
    else:
        normalized_energy = energy

    # J_m = α_m × T_m + (1-α_m) × E_m
    objective = weight * normalized_delay + (1.0 - weight) * normalized_energy

    return objective
```

#### 4.3.3 多设备目标函数
```python
def calculate_multi_device_objective(self, delays: List[float], energies: List[float],
                                   weights: List[float]) -> Dict[str, Any]:
    """
    计算多设备目标函数
    J = (1/M) × Σ_m [α_m × T_m + (1-α_m) × E_m]
    """
    device_objectives = []
    total_weighted_delay = 0.0
    total_weighted_energy = 0.0

    for delay, energy, weight in zip(delays, energies, weights):
        obj = self.calculate_objective_function(delay, energy, weight)
        device_objectives.append(obj)

        total_weighted_delay += weight * delay
        total_weighted_energy += (1.0 - weight) * energy

    # 计算平均目标函数值
    avg_objective = sum(device_objectives) / len(device_objectives)

    return {
        'device_objectives': device_objectives,
        'average_objective': avg_objective,
        'total_weighted_delay': total_weighted_delay,
        'total_weighted_energy': total_weighted_energy,
        'objective_std': np.std(device_objectives)
    }
```

#### 4.3.4 权重阈值分析
```python
def get_battery_thresholds(self) -> Dict[str, float]:
    """获取不同权重水平对应的电池阈值"""
    battery_thresholds = {}

    # 关键权重点
    weight_points = [0.1, 0.25, 0.5, 0.75, 0.9]

    for weight in weight_points:
        if weight == 0.5:
            threshold = self.theta  # 0.4
        else:
            # 反向计算: battery_ratio = θ + ln((1-α)/α) / β
            threshold = self.theta + np.log((1.0 - weight) / weight) / self.beta

        battery_thresholds[f'weight_{weight}'] = max(0.0, min(1.0, threshold))

    return battery_thresholds
```

**阈值分析结果**:
- **weight_0.9**: 电池约65%时开始性能优先
- **weight_0.5**: 电池40%时平衡模式
- **weight_0.1**: 电池约15%时极度节能模式

## 5. 神经网络实现详细分析 (networks/)

### 5.1 MADDPG算法实现 (maddpg.py)

#### 5.1.1 Actor网络架构
```python
class Actor(nn.Module):
    """
    Actor网络 - 分散执行
    输入: 本地状态 [电池比率, 频率比率] (2维)
    输出: 动作 [分割点比率, 服务器选择, 频率比率] (3维)
    """

    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = None):
        super(Actor, self).__init__()

        if hidden_dims is None:
            hidden_dims = [128, 64]  # 默认隐藏层维度

        # 构建网络层
        layers = []
        prev_dim = state_dim  # 2

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)  # 防止过拟合
            ])
            prev_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(prev_dim, action_dim))  # 3
        layers.append(nn.Tanh())  # 动作范围 [-1, 1]

        self.network = nn.Sequential(*layers)
```

**Actor网络分析**:
- **输入维度**: 2 (电池比率 + 频率比率)
- **隐藏层**: [128, 64] 全连接层
- **输出维度**: 3 (分割点 + 服务器选择 + 频率)
- **激活函数**: ReLU (隐藏层) + Tanh (输出层)
- **正则化**: Dropout (0.1) 防止过拟合

#### 5.1.2 Critic网络架构
```python
class Critic(nn.Module):
    """
    Critic网络 - 集中训练
    输入: 全局状态 + 全局动作
    输出: Q值 (标量)
    """

    def __init__(self, global_state_dim: int, global_action_dim: int,
                 hidden_dims: List[int] = None):
        super(Critic, self).__init__()

        if hidden_dims is None:
            hidden_dims = [128, 64]

        # 输入: 全局状态 + 全局动作
        input_dim = global_state_dim + global_action_dim  # 8 + 12 = 20

        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim

        # 输出单个Q值
        layers.append(nn.Linear(prev_dim, 1))

        self.network = nn.Sequential(*layers)
```

**Critic网络分析**:
- **输入维度**: 20 (4设备×2状态 + 4设备×3动作)
- **隐藏层**: [128, 64] 全连接层
- **输出维度**: 1 (Q值)
- **集中训练**: 使用全局信息评估动作价值

#### 5.1.3 动作选择机制
```python
def act(self, state: np.ndarray, add_noise: bool = True,
        noise_scale: float = 1.0) -> np.ndarray:
    """使用Actor网络选择动作"""
    state_tensor = torch.FloatTensor(state).unsqueeze(0)

    self.actor.eval()
    with torch.no_grad():
        action = self.actor(state_tensor).cpu().numpy()[0]
    self.actor.train()

    if add_noise:
        noise = self.noise.sample() * noise_scale  # OU噪声
        action += noise
        action = np.clip(action, -1.0, 1.0)  # 限制到[-1,1]

    return action
```

**动作空间映射**:
```python
# 在环境中将[-1,1]动作映射到实际参数
def decode_action(self, action: np.ndarray, device_id: int) -> Dict[str, Any]:
    """将标准化动作解码为实际参数"""

    # 1. 分割点比率 [-1,1] → [0, max_partition_point]
    partition_ratio = (action[0] + 1.0) / 2.0  # [0,1]
    max_partition = len(task.valid_partition_points) - 1
    partition_idx = int(partition_ratio * max_partition)
    partition_point = task.valid_partition_points[partition_idx]

    # 2. 服务器选择 [-1,1] → [0, num_servers-1]
    server_ratio = (action[1] + 1.0) / 2.0  # [0,1]
    server_idx = int(server_ratio * (self.num_servers - 1))

    # 3. 频率比率 [-1,1] → [f_min, f_max]
    freq_ratio = (action[2] + 1.0) / 2.0  # [0,1]
    frequency = device.f_min + freq_ratio * (device.f_max - device.f_min)

    return {
        'partition_point': partition_point,
        'target_server': server_idx,
        'device_frequency': frequency
    }
```

#### 5.1.4 OU噪声探索机制
```python
class OUNoise:
    """Ornstein-Uhlenbeck噪声用于连续动作空间探索"""

    def __init__(self, action_dim: int, mu: float = 0.0, theta: float = 0.15,
                 sigma: float = 0.2):
        self.action_dim = action_dim
        self.mu = mu          # 长期均值
        self.theta = theta    # 回归速度
        self.sigma = sigma    # 噪声强度
        self.state = np.ones(action_dim) * mu

    def sample(self) -> np.ndarray:
        """生成相关噪声"""
        dx = self.theta * (self.mu - self.state) + \
             self.sigma * np.random.randn(self.action_dim)
        self.state += dx
        return self.state
```

**OU噪声特点**:
- **时间相关性**: 连续时间步的噪声相关
- **均值回归**: 噪声趋向于回归到均值
- **适合连续控制**: 比高斯噪声更适合连续动作空间

#### 5.1.5 Critic网络更新
```python
def update_critic(self, experiences: List[Tuple], other_agents: List['MADDPGAgent']):
    """更新Critic网络"""
    states, actions, rewards, next_states, dones = zip(*experiences)

    # 转换为张量
    batch_size = len(states)
    states = torch.FloatTensor(np.array(states))
    actions = torch.FloatTensor(np.array(actions))
    rewards = torch.FloatTensor(rewards)
    next_states = torch.FloatTensor(np.array(next_states))
    dones = torch.BoolTensor(dones)

    # 计算目标Q值
    with torch.no_grad():
        # 获取下一状态的动作 (使用目标Actor)
        next_actions = []
        for i, agent in enumerate(other_agents):
            next_action = agent.target_actor(next_states[:, i])
            next_actions.append(next_action)
        next_actions = torch.cat(next_actions, dim=1)

        # 计算目标Q值
        next_global_states = next_states.view(batch_size, -1)
        target_q = self.target_critic(next_global_states, next_actions)
        target_q = rewards.unsqueeze(1) + (self.gamma * target_q * (~dones).unsqueeze(1))

    # 当前Q值
    global_states = states.view(batch_size, -1)
    current_q = self.critic(global_states, actions.view(batch_size, -1))

    # Critic损失 (MSE)
    critic_loss = F.mse_loss(current_q, target_q)

    # 更新Critic
    self.critic_optimizer.zero_grad()
    critic_loss.backward()
    torch.nn.utils.clip_grad_norm_(self.critic.parameters(), 1.0)  # 梯度裁剪
    self.critic_optimizer.step()

    return critic_loss.item()
```

#### 5.1.6 Actor网络更新
```python
def update_actor(self, experiences: List[Tuple], other_agents: List['MADDPGAgent']):
    """更新Actor网络"""
    states, actions, _, _, _ = zip(*experiences)

    batch_size = len(states)
    states = torch.FloatTensor(np.array(states))

    # 获取所有智能体的动作
    all_actions = []
    for i, agent in enumerate(other_agents):
        if i == self.agent_id:
            action = self.actor(states[:, i])  # 当前智能体使用可训练Actor
        else:
            action = agent.actor(states[:, i]).detach()  # 其他智能体使用固定Actor
        all_actions.append(action)

    all_actions = torch.cat(all_actions, dim=1)

    # Actor损失 (负Q值，最大化Q值)
    global_states = states.view(batch_size, -1)
    actor_loss = -self.critic(global_states, all_actions).mean()

    # 更新Actor
    self.actor_optimizer.zero_grad()
    actor_loss.backward()
    torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
    self.actor_optimizer.step()

    return actor_loss.item()
```

#### 5.1.7 软更新机制
```python
def soft_update(self):
    """软更新目标网络"""
    # 更新目标Actor
    for target_param, param in zip(self.target_actor.parameters(),
                                  self.actor.parameters()):
        target_param.data.copy_(self.tau * param.data +
                               (1.0 - self.tau) * target_param.data)

    # 更新目标Critic
    for target_param, param in zip(self.target_critic.parameters(),
                                  self.critic.parameters()):
        target_param.data.copy_(self.tau * param.data +
                               (1.0 - self.tau) * target_param.data)
```

**软更新分析**:
- **更新公式**: θ_target = τ × θ_current + (1-τ) × θ_target
- **τ = 0.005**: 非常缓慢的更新，保证训练稳定性
- **目的**: 避免目标网络变化过快导致训练不稳定

#### 5.1.8 经验回放缓冲区
```python
class ReplayBuffer:
    """经验回放缓冲区"""

    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)  # 10000

    def push(self, experience: Tuple):
        """存储经验"""
        self.buffer.append(experience)

    def sample(self, batch_size: int) -> List[Tuple]:
        """随机采样经验"""
        return random.sample(self.buffer, batch_size)

    def __len__(self) -> int:
        return len(self.buffer)
```

#### 5.1.9 噪声调度策略
```python
def update_noise_scale(self, episode: int, max_episodes: int):
    """更新探索噪声强度"""
    decay_episodes = max_episodes * self.noise_decay_episodes  # 80%的episode用于衰减

    if episode < decay_episodes:
        # 线性衰减
        progress = episode / decay_episodes
        self.current_noise_scale = (self.initial_noise * (1 - progress) +
                                   self.final_noise * progress)
    else:
        self.current_noise_scale = self.final_noise

    # 更新所有智能体的噪声
    for agent in self.agents:
        agent.noise.sigma = self.current_noise_scale
```

**噪声调度分析**:
- **初始噪声**: 0.4 (高探索)
- **最终噪声**: 0.08 (低探索)
- **衰减策略**: 前80%的episode线性衰减
- **目的**: 早期充分探索，后期精确利用

### 5.2 ResNet-50早期退出网络 (resnet50_early_exit.py)

#### 5.2.1 早期退出分支架构
```python
class EarlyExitBranch(nn.Module):
    """早期退出分支用于中间预测"""

    def __init__(self, in_channels, num_classes=10):
        super(EarlyExitBranch, self).__init__()
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))  # 全局平均池化
        self.flatten = nn.Flatten()                   # 展平
        self.fc = nn.Linear(in_channels, num_classes) # 分类层

    def forward(self, x):
        out = self.avgpool(x)    # [B, C, H, W] → [B, C, 1, 1]
        out = self.flatten(out)  # [B, C, 1, 1] → [B, C]
        out = self.fc(out)       # [B, C] → [B, num_classes]
        return out
```

#### 5.2.2 ResNet-50主体架构
```python
class ResNet50EarlyExit(nn.Module):
    """带早期退出的ResNet-50网络"""

    def __init__(self, num_classes=10):
        super(ResNet50EarlyExit, self).__init__()

        # 初始卷积层
        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm2d(64)

        # ResNet层
        self.layer1 = self._make_layer(Bottleneck, 64, 3, stride=1)   # 256通道
        self.layer2 = self._make_layer(Bottleneck, 128, 4, stride=2)  # 512通道
        self.layer3 = self._make_layer(Bottleneck, 256, 6, stride=2)  # 1024通道
        self.layer4 = self._make_layer(Bottleneck, 512, 3, stride=2)  # 2048通道

        # 早期退出分支 (基于准确率分析)
        self.early_exit_1 = EarlyExitBranch(256, num_classes)   # Layer1后 (77.00%)
        self.early_exit_2 = EarlyExitBranch(512, num_classes)   # Layer2后 (87.31%)
        self.early_exit_3 = EarlyExitBranch(1024, num_classes)  # Layer3[2]后 (90.38%)
        self.early_exit_4 = EarlyExitBranch(1024, num_classes)  # Layer3[5]后 (91.56%)
        self.early_exit_5 = EarlyExitBranch(2048, num_classes)  # Layer4[0]后 (92.03%)

        # 最终分类器
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(2048, num_classes)  # 完整模型 (92.40%)
```

#### 5.2.3 前向传播与早期退出
```python
def forward(self, x, exit_point: str = "Full", partition_point: int = 0):
    """
    前向传播支持早期退出和分割

    Args:
        x: 输入张量 [B, 3, 32, 32] (CIFAR-10)
        exit_point: 早期退出点 ("Exit_1", "Exit_2", ..., "Full")
        partition_point: 分割点 (0表示完全本地执行)

    Returns:
        输出张量或中间特征
    """
    # 初始卷积
    out = F.relu(self.bn1(self.conv1(x)))  # [B, 64, 16, 16]

    # Layer 1
    out = self.layer1(out)  # [B, 256, 16, 16]
    if exit_point == "Exit_1":
        return self.early_exit_1(out)  # 77.00%准确率

    # Layer 2
    out = self.layer2(out)  # [B, 512, 8, 8]
    if exit_point == "Exit_2":
        return self.early_exit_2(out)  # 87.31%准确率

    # Layer 3 (细粒度退出)
    layer3_blocks = list(self.layer3.children())
    for i, block in enumerate(layer3_blocks):
        out = block(out)  # [B, 1024, 4, 4]
        if exit_point == "Exit_3" and i == 2:  # 第3个block后
            return self.early_exit_3(out)  # 90.38%准确率
        if exit_point == "Exit_4" and i == 5:  # 第6个block后
            return self.early_exit_4(out)  # 91.56%准确率

    # Layer 4
    layer4_blocks = list(self.layer4.children())
    out = layer4_blocks[0](out)  # [B, 2048, 2, 2]
    if exit_point == "Exit_5":
        return self.early_exit_5(out)  # 92.03%准确率

    # 完成Layer 4
    for block in layer4_blocks[1:]:
        out = block(out)

    # 最终分类
    out = self.avgpool(out)      # [B, 2048, 1, 1]
    out = torch.flatten(out, 1)  # [B, 2048]
    out = self.fc(out)           # [B, num_classes]

    return out  # 92.40%准确率
```

#### 5.2.4 分割计算支持
```python
def forward_with_partition(self, x, partition_point: int, exit_point: str = "Full"):
    """
    支持分割计算的前向传播

    Args:
        x: 输入张量
        partition_point: 分割点层索引
        exit_point: 目标早期退出点

    Returns:
        分割计算结果字典
    """
    if partition_point == 0:
        # 完全本地执行
        output = self.forward(x, exit_point)
        return {
            'local_output': output,
            'edge_input': None,
            'partition_point': partition_point,
            'exit_point': exit_point,
            'requires_edge': False
        }
    else:
        # 部分卸载 - 计算到分割点
        intermediate_features = self._forward_to_layer(x, partition_point)
        return {
            'local_output': None,
            'edge_input': intermediate_features,
            'partition_point': partition_point,
            'exit_point': exit_point,
            'requires_edge': True
        }
```

## 6. 强化学习环境详细分析 (environment/)

### 6.1 EdgeComputingEnvironment类

#### 6.1.1 环境初始化
```python
class EdgeComputingEnvironment:
    def __init__(self, num_devices: int = None, num_servers: int = None,
                 random_seed: int = None):
        # 环境配置
        self.num_devices = num_devices or 4      # 4个设备(智能体)
        self.num_servers = num_servers or 4      # 4个边缘服务器
        self.max_steps = 100                     # 每个episode最大步数

        # 初始化设备 (随机初始电池: 50%-100%)
        self.devices = [
            LocalDevice(device_id=i, initial_battery_ratio=np.random.uniform(0.5, 1.0))
            for i in range(self.num_devices)
        ]

        # 初始化服务器
        self.servers = [
            EdgeServer(server_id=i)
            for i in range(self.num_servers)
        ]

        # 性能模型
        self.energy_model = EnergyModel()
        self.delay_model = DelayModel()
        self.weight_mechanism = AdaptiveWeightMechanism()

        # 状态和动作空间维度
        self.state_dim = self._calculate_state_dim()    # 11维
        self.action_dim = self._calculate_action_dim()  # 3维
```

#### 6.1.2 状态空间设计
```python
def _calculate_state_dim(self) -> int:
    """计算每个智能体的状态空间维度"""
    # 本地状态: [电池比率, 频率比率]
    local_state_dim = 2

    # 全局状态: 每个服务器的[负载比率, 可用容量比率]
    global_state_dim = self.num_servers * 2  # 4 × 2 = 8

    # 任务状态: [准确率要求]
    task_state_dim = 1

    return local_state_dim + global_state_dim + task_state_dim  # 2 + 8 + 1 = 11

def _get_states(self) -> List[np.ndarray]:
    """获取所有智能体的当前状态"""
    states = []

    for i, (device, task) in enumerate(zip(self.devices, self.tasks)):
        # 本地状态
        local_state = device.get_state()  # [battery_ratio, frequency_ratio]

        # 全局状态 (所有服务器信息)
        server_states = []
        for server in self.servers:
            server_state = server.get_state()  # [load_ratio, available_capacity_ratio]
            server_states.extend(server_state)

        # 任务状态
        task_state = [task.accuracy_requirement]  # [0.7-0.91]

        # 组合完整状态
        full_state = np.concatenate([
            local_state,           # [2]
            np.array(server_states), # [8]
            np.array(task_state)   # [1]
        ])  # 总维度: 11

        states.append(full_state.astype(np.float32))

    return states
```

**状态空间分析**:
- **本地状态** (2维): 设备自身的电池和频率状态
- **全局状态** (8维): 所有服务器的负载和容量信息
- **任务状态** (1维): 当前任务的准确率要求
- **总维度**: 11维连续状态空间
- **归一化**: 所有状态值都归一化到[0,1]范围

#### 6.1.3 动作空间设计
```python
def _calculate_action_dim(self) -> int:
    """计算每个智能体的动作空间维度"""
    # 动作: [分割点比率, 服务器选择, 频率比率]
    # partition_point_ratio: 连续 [-1, 1] (映射到有效分割点)
    # server_selection: 连续 [-1, 1] (映射到离散服务器ID)
    # frequency_ratio: 连续 [-1, 1] (映射到频率范围)
    return 3

def _decode_actions(self, actions: List[np.ndarray]) -> List[Dict[str, Any]]:
    """将标准化动作解码为实际参数"""
    decoded_actions = []

    for i, action in enumerate(actions):
        device = self.devices[i]
        task = self.tasks[i]

        # 1. 分割点解码 [-1,1] → 有效分割点索引
        partition_ratio = (action[0] + 1.0) / 2.0  # [0,1]
        valid_partitions = task.valid_partition_points
        partition_idx = int(partition_ratio * (len(valid_partitions) - 1))
        partition_point = valid_partitions[partition_idx]

        # 2. 服务器选择 [-1,1] → [0, num_servers-1]
        server_ratio = (action[1] + 1.0) / 2.0  # [0,1]
        server_id = int(server_ratio * (self.num_servers - 1))

        # 3. 频率设置 [-1,1] → [f_min, f_max]
        freq_ratio = (action[2] + 1.0) / 2.0  # [0,1]
        frequency = device.f_min + freq_ratio * (device.f_max - device.f_min)

        decoded_action = {
            'partition_point': partition_point,
            'server_id': server_id,
            'frequency': frequency,
            'raw_action': action.copy()
        }
        decoded_actions.append(decoded_action)

    return decoded_actions
```

**动作空间分析**:
- **维度**: 3维连续动作空间，范围[-1,1]
- **分割点**: 映射到ResNet-50的有效Conv2d层索引
- **服务器选择**: 映射到4个边缘服务器之一
- **频率控制**: 映射到设备频率范围[1.2GHz, 1.8GHz]

#### 6.1.4 任务执行逻辑
```python
def _execute_tasks(self, decoded_actions: List[Dict[str, Any]]) -> Dict[str, Any]:
    """执行所有设备的推理任务"""
    device_delays = []
    device_energies = []
    server_allocations = []
    constraint_violations = []

    for i, (action, task, device) in enumerate(zip(decoded_actions, self.tasks, self.devices)):
        # 设置任务配置
        task.set_partition_point(action['partition_point'])
        task.set_target_server(action['server_id'])
        task.set_device_frequency(action['frequency'])
        device.set_frequency(action['frequency'])

        # 获取计算需求
        local_flops = task.get_local_flops()
        edge_flops = task.get_edge_flops()
        data_size = task.get_transmission_data_size()

        # 获取服务器和网络参数
        server = self.servers[action['server_id']]
        network_condition = self.network_conditions[i]

        # 计算延迟
        delay_result = self.delay_model.calculate_total_delay(
            local_flops=local_flops,
            edge_flops=edge_flops,
            data_size_bits=data_size,
            device_frequency=action['frequency'],
            server_frequency=server.f_server,
            transmission_rate=network_condition['transmission_rate']
        )

        total_delay = delay_result['total_delay']
        device_delays.append(total_delay)

        # 计算能耗
        energy_result = self.energy_model.calculate_total_device_energy(
            local_flops=local_flops,
            device_frequency=action['frequency'],
            g_device=device.g_device,
            data_size_bits=data_size,
            transmission_rate=network_condition['transmission_rate'],
            total_time=total_delay,
            kappa=device.kappa,
            tx_power=device.p_tx,
            static_power=device.p_static
        )

        total_energy = energy_result['total_energy']
        device_energies.append(total_energy)

        # 检查服务器分配 (仅当需要边缘计算时)
        if edge_flops > 0:
            server_allocated = server.allocate_task(
                task_id=task.task_id,
                required_flops=edge_flops,
                device_id=device.device_id
            )
        else:
            server_allocated = True  # 本地执行无需服务器
        server_allocations.append(server_allocated)

        # 检查约束违反
        violations = {
            'accuracy': not task.is_accuracy_satisfied(),
            'delay': total_delay > config.optimization.t_max,  # 50ms
            'battery': not device.consume_energy(total_energy),
            'server_capacity': not server_allocated
        }
        constraint_violations.append(violations)

    return {
        'device_delays': device_delays,
        'device_energies': device_energies,
        'server_allocations': server_allocations,
        'constraint_violations': constraint_violations
    }
```

#### 6.1.5 奖励函数设计
```python
def _calculate_rewards(self, execution_results: Dict[str, Any]) -> List[float]:
    """计算每个智能体的个体奖励"""
    rewards = []
    device_delays = execution_results['device_delays']
    device_energies = execution_results['device_energies']
    violations = execution_results['constraint_violations']

    for i, (device, task) in enumerate(zip(self.devices, self.tasks)):
        # 获取自适应权重
        alpha = device.get_adaptive_weight()  # 基于电池状态

        # 归一化延迟和能耗
        normalized_delay = device_delays[i] / self.max_delay_norm    # 0.05s
        normalized_energy = device_energies[i] / self.max_energy_norm  # 72.0J

        # 基础奖励 (负目标函数)
        base_reward = -(alpha * normalized_delay + (1 - alpha) * normalized_energy)

        # 约束惩罚
        penalty = 0.0

        if violations[i]['accuracy']:
            penalty += config.environment.accuracy_violation_penalty  # 200.0
            self.episode_stats['accuracy_violations'] += 1

        if violations[i]['delay']:
            penalty += config.environment.delay_violation_penalty     # 50.0
            self.episode_stats['delay_violations'] += 1

        if violations[i]['battery']:
            penalty += config.environment.battery_depletion_penalty   # 100.0
            self.episode_stats['battery_depletions'] += 1

        if violations[i]['server_capacity']:
            penalty += config.environment.server_overload_penalty     # 30.0

        # 最终奖励
        reward = base_reward - penalty
        rewards.append(reward)

    return rewards
```

**奖励函数分析**:
- **基础奖励**: 负的加权目标函数 -(α×延迟 + (1-α)×能耗)
- **自适应权重**: α基于电池状态动态调整
- **约束惩罚**:
  - 准确率违约: -200
  - 延迟违约: -50
  - 电池耗尽: -100
  - 服务器过载: -30
- **个体化**: 每个智能体根据自身状态获得不同奖励

#### 6.1.6 环境步进逻辑
```python
def step(self, actions: List[np.ndarray]) -> Tuple[List[np.ndarray], List[float],
                                                  List[bool], Dict[str, Any]]:
    """执行环境中的一步"""
    # 1. 解码动作
    decoded_actions = self._decode_actions(actions)

    # 2. 执行任务
    execution_results = self._execute_tasks(decoded_actions)

    # 3. 计算奖励
    rewards = self._calculate_rewards(execution_results)

    # 4. 更新环境状态
    self._update_environment_state(execution_results)

    # 5. 检查是否结束
    self.current_step += 1
    dones = self._check_done()

    # 6. 获取下一状态
    next_states = self._get_states()

    # 7. 准备信息字典
    info = self._get_info(execution_results, decoded_actions)

    return next_states, rewards, dones, info

def _check_done(self) -> List[bool]:
    """检查episode是否结束"""
    # 所有智能体同时结束
    done = (self.current_step >= self.max_steps or
            any(device.is_depleted() for device in self.devices))

    return [done] * self.num_devices
```

#### 6.1.7 网络条件更新
```python
def _update_network_conditions(self):
    """更新所有设备的网络条件"""
    self.network_conditions = []

    for i in range(self.num_devices):
        condition = {
            'transmission_rate': np.random.uniform(
                config.network.rate_min,  # 15.5 Mbps
                config.network.rate_max   # 25.0 Mbps
            )
        }
        self.network_conditions.append(condition)
```

**网络条件特点**:
- **动态变化**: 每个时间步随机更新
- **异构性**: 不同设备有不同的网络条件
- **现实性**: 基于实际边缘网络的带宽范围

## 7. 基线算法详细分析 (baselines.py)

### 7.1 基线算法架构
```python
class BaselineAlgorithm:
    """基线算法基类"""

    def __init__(self, name: str):
        self.name = name

    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """为所有设备选择动作"""
        raise NotImplementedError

    def reset(self):
        """重置算法状态"""
        pass
```

### 7.2 Local-Only基线算法
```python
class LocalOnlyBaseline(BaselineAlgorithm):
    """完全本地执行基线算法"""

    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """选择完全本地执行的动作"""
        actions = []

        for i in range(env.num_devices):
            # 完全本地执行: 分割点=0, 服务器不使用, 最大频率
            action = np.array([
                -1.0,  # partition_point_ratio → 0 (完全本地)
                0.0,   # server_selection (不使用)
                1.0    # frequency_ratio → 最大频率
            ])
            actions.append(action)

        return actions
```

**Local-Only策略分析**:
- **分割策略**: 完全本地执行，无数据传输
- **频率策略**: 使用最大频率以最小化延迟
- **优势**: 无通信开销，无服务器依赖
- **劣势**: 高本地能耗，受设备计算能力限制

### 7.3 Edge-Only基线算法
```python
class EdgeOnlyBaseline(BaselineAlgorithm):
    """完全边缘执行基线算法"""

    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """选择完全边缘执行的动作"""
        actions = []

        # 找到可用容量最大的服务器
        server_capacities = [server.get_available_capacity() for server in env.servers]
        best_server_idx = np.argmax(server_capacities)

        for i in range(env.num_devices):
            # 完全卸载: 分割点=最大, 最佳服务器, 最小频率
            action = np.array([
                1.0,   # partition_point_ratio → 最大 (完全卸载)
                (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,  # 映射到[-1,1]
                -1.0   # frequency_ratio → 最小频率 (本地计算最少)
            ])
            actions.append(action)

        return actions
```

**Edge-Only策略分析**:
- **分割策略**: 完全卸载到边缘服务器
- **服务器选择**: 选择可用容量最大的服务器
- **频率策略**: 最小频率节省本地能耗
- **优势**: 利用服务器高性能，节省本地能耗
- **劣势**: 高通信开销，依赖网络条件

### 7.4 Greedy-Accuracy基线算法
```python
class GreedyAccuracyBaseline(BaselineAlgorithm):
    """贪心准确率基线算法"""

    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """优先考虑准确率的动作选择"""
        actions = []

        for i in range(env.num_devices):
            device = env.devices[i]
            task = env.tasks[i]

            # 选择完整模型执行 (无早期退出)
            # 根据电池水平决定本地vs边缘
            battery_ratio = device.get_battery_ratio()

            if battery_ratio > 0.7:
                # 高电量: 本地执行 + 高频率
                action = np.array([
                    -1.0,  # 完全本地
                    0.0,   # 服务器不使用
                    1.0    # 最大频率
                ])
            else:
                # 低电量: 卸载到边缘
                server_loads = [server.current_load for server in env.servers]
                best_server_idx = np.argmin(server_loads)  # 负载最小的服务器

                action = np.array([
                    1.0,   # 完全卸载
                    (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                    -1.0   # 最小频率
                ])

            actions.append(action)

        return actions
```

**Greedy-Accuracy策略分析**:
- **准确率优先**: 总是选择完整模型 (92.40%准确率)
- **电池感知**: 高电量本地执行，低电量边缘执行
- **服务器选择**: 选择负载最小的服务器
- **适用场景**: 对准确率要求极高的应用

### 7.5 Greedy-Energy基线算法
```python
class GreedyEnergyBaseline(BaselineAlgorithm):
    """贪心能耗基线算法"""

    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """优先考虑能耗效率的动作选择"""
        actions = []

        for i in range(env.num_devices):
            device = env.devices[i]
            task = env.tasks[i]

            # 找到满足准确率要求的最小早期退出点
            min_exit = config.get_early_exit_for_accuracy(task.accuracy_requirement)

            # 总是偏好卸载以节省本地能耗
            server_capacities = [server.get_available_capacity() for server in env.servers]
            best_server_idx = np.argmax(server_capacities)

            action = np.array([
                1.0,   # 完全卸载节省本地能耗
                (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                -1.0   # 最小频率节省能耗
            ])

            actions.append(action)

        return actions
```

**Greedy-Energy策略分析**:
- **能耗优先**: 最小化总能耗消耗
- **早期退出**: 选择满足准确率的最小退出点
- **卸载偏好**: 总是选择卸载以节省本地能耗
- **频率策略**: 最小频率进一步节能

### 7.6 Random基线算法
```python
class RandomBaseline(BaselineAlgorithm):
    """随机基线算法"""

    def __init__(self, seed: int = None):
        super().__init__("Random")
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """随机选择动作"""
        actions = []

        for i in range(env.num_devices):
            action = np.array([
                np.random.uniform(-1.0, 1.0),  # 随机分割点
                np.random.uniform(-1.0, 1.0),  # 随机服务器
                np.random.uniform(-1.0, 1.0)   # 随机频率
            ])
            actions.append(action)

        return actions
```

**Random策略分析**:
- **完全随机**: 所有决策变量随机选择
- **基准作用**: 作为其他算法的性能下界
- **可重现性**: 使用固定种子保证结果一致性

### 7.7 Adaptive-Greedy基线算法
```python
class AdaptiveGreedyBaseline(BaselineAlgorithm):
    """自适应贪心基线算法"""

    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """基于系统状态的自适应动作选择"""
        actions = []

        # 计算系统级电池水平
        avg_battery = np.mean([device.get_battery_ratio() for device in env.devices])

        # 计算服务器负载分布
        server_loads = [server.current_load for server in env.servers]
        avg_server_load = np.mean(server_loads)

        for i in range(env.num_devices):
            device = env.devices[i]
            task = env.tasks[i]
            battery_ratio = device.get_battery_ratio()

            # 基于电池和系统状态的自适应策略
            if battery_ratio > 0.8 and avg_server_load > 0.6:
                # 高电量 + 服务器繁忙 → 本地执行
                action = np.array([
                    -1.0,  # 本地执行
                    0.0,   # 服务器不使用
                    0.5    # 中等频率
                ])
            elif battery_ratio < 0.3:
                # 低电量 → 激进卸载
                best_server_idx = np.argmin(server_loads)
                action = np.array([
                    1.0,   # 完全卸载
                    (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                    -1.0   # 最小频率
                ])
            else:
                # 平衡方法 → 部分卸载
                best_server_idx = np.argmin(server_loads)
                partition_ratio = 0.5 - battery_ratio  # 电池越低卸载越多

                action = np.array([
                    partition_ratio,  # 自适应分割
                    (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                    battery_ratio * 2.0 - 1.0  # 基于电池的频率
                ])

            actions.append(action)

        return actions
```

**Adaptive-Greedy策略分析**:
- **多因素决策**: 考虑个体电池、系统电池、服务器负载
- **分层策略**:
  - 高电量+服务器忙 → 本地执行
  - 低电量 → 激进卸载
  - 中等情况 → 自适应分割
- **动态调整**: 分割比例和频率基于电池状态动态调整

### 7.8 基线算法评估框架
```python
def evaluate_baseline(baseline: BaselineAlgorithm, env: EdgeComputingEnvironment,
                     num_episodes: int = 100) -> Dict[str, Any]:
    """评估基线算法性能"""

    episode_rewards = []
    episode_energies = []
    episode_delays = []
    episode_accuracies = []
    constraint_violations = {
        'accuracy': 0, 'delay': 0, 'battery': 0, 'server': 0
    }

    for episode in range(num_episodes):
        # 重置环境和算法
        states = env.reset()
        baseline.reset()

        episode_reward = 0.0
        episode_energy = 0.0
        episode_delay = 0.0
        step_count = 0

        while step_count < env.max_steps:
            # 获取基线算法动作
            actions = baseline.select_actions(env)

            # 执行环境步进
            next_states, rewards, dones, info = env.step(actions)

            # 累积指标
            episode_reward += sum(rewards)
            episode_energy += sum(info['execution_results']['device_energies'])
            episode_delay += sum(info['execution_results']['device_delays'])

            # 统计约束违反
            for violations in info['execution_results']['constraint_violations']:
                for constraint_type, violated in violations.items():
                    if violated:
                        constraint_violations[constraint_type] += 1

            step_count += 1
            states = next_states

            if all(dones):
                break

        episode_rewards.append(episode_reward)
        episode_energies.append(episode_energy)
        episode_delays.append(episode_delay)

        # 计算平均准确率
        avg_accuracy = np.mean([task.achieved_accuracy for task in env.tasks])
        episode_accuracies.append(avg_accuracy)

    # 计算统计结果
    results = {
        'algorithm': baseline.name,
        'num_episodes': num_episodes,
        'avg_reward': np.mean(episode_rewards),
        'std_reward': np.std(episode_rewards),
        'avg_energy': np.mean(episode_energies),
        'std_energy': np.std(episode_energies),
        'avg_delay': np.mean(episode_delays),
        'std_delay': np.std(episode_delays),
        'avg_accuracy': np.mean(episode_accuracies),
        'std_accuracy': np.std(episode_accuracies),
        'constraint_violations': constraint_violations,
        'violation_rate': {
            k: v / (num_episodes * env.num_devices)
            for k, v in constraint_violations.items()
        }
    }

    return results
```

**评估框架特点**:
- **多指标评估**: 奖励、能耗、延迟、准确率
- **约束统计**: 各类约束违反的频率统计
- **统计分析**: 均值、标准差、违约率
- **可重现性**: 固定随机种子保证结果一致

## 8. 训练和评估流程详细分析 (main.py)

### 8.1 MADDPGTrainer类架构
```python
class MADDPGTrainer:
    """MADDPG训练和评估管理器"""

    def __init__(self, env: EdgeComputingEnvironment, save_dir: str = "results"):
        self.env = env
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)

        # 初始化MADDPG
        self.maddpg = MADDPG(
            num_agents=env.num_devices,    # 4个智能体
            state_dim=env.state_dim,       # 11维状态
            action_dim=env.action_dim      # 3维动作
        )

        # 训练统计
        self.training_stats = {
            'episode_rewards': [],         # 每个episode的总奖励
            'episode_energies': [],        # 每个episode的总能耗
            'episode_delays': [],          # 每个episode的总延迟
            'episode_accuracies': [],      # 每个episode的平均准确率
            'episode_violations': [],      # 每个episode的约束违反次数
            'avg_battery_levels': []       # 每个episode的平均电池水平
        }

        # 基线算法
        self.baselines = create_baseline_algorithms()
```

### 8.2 MADDPG训练流程
```python
def train(self, num_episodes: int = None, save_frequency: int = 100,
          eval_frequency: int = 50) -> Dict[str, Any]:
    """训练MADDPG算法"""

    if num_episodes is None:
        num_episodes = config.maddpg.max_episodes  # 1000

    print(f"开始MADDPG训练，共{num_episodes}个episode...")
    print(f"环境: {self.env.num_devices}个设备, {self.env.num_servers}个服务器")
    print(f"状态维度: {self.env.state_dim}, 动作维度: {self.env.action_dim}")

    for episode in range(num_episodes):
        # 重置环境
        states = self.env.reset()
        self.maddpg.reset_noise()  # 重置OU噪声

        episode_reward = 0.0
        episode_energy = 0.0
        episode_delay = 0.0
        episode_violations = 0
        step_count = 0

        while step_count < self.env.max_steps:  # 最大100步
            # 从MADDPG获取动作
            actions = self.maddpg.act(states, add_noise=True)

            # 执行环境步进
            next_states, rewards, dones, info = self.env.step(actions)

            # 存储经验到回放缓冲区
            self.maddpg.step(states, actions, rewards, next_states, dones)

            # 累积统计信息
            episode_reward += sum(rewards)
            episode_energy += sum(info['execution_results']['device_energies'])
            episode_delay += sum(info['execution_results']['device_delays'])

            # 统计约束违反
            for violations in info['execution_results']['constraint_violations']:
                episode_violations += sum(violations.values())

            step_count += 1
            states = next_states

            if all(dones):
                break

        # 更新噪声强度 (探索衰减)
        self.maddpg.update_noise_scale(episode, num_episodes)

        # 存储episode统计
        self.training_stats['episode_rewards'].append(episode_reward)
        self.training_stats['episode_energies'].append(episode_energy)
        self.training_stats['episode_delays'].append(episode_delay)
        self.training_stats['episode_violations'].append(episode_violations)

        # 计算平均准确率和电池水平
        avg_accuracy = np.mean([task.achieved_accuracy for task in self.env.tasks])
        avg_battery = np.mean([device.get_battery_ratio() for device in self.env.devices])

        self.training_stats['episode_accuracies'].append(avg_accuracy)
        self.training_stats['avg_battery_levels'].append(avg_battery)

        # 打印进度
        if (episode + 1) % 10 == 0:
            print(f"Episode {episode + 1}/{num_episodes}: "
                  f"Reward={episode_reward:.2f}, "
                  f"Energy={episode_energy:.2f}, "
                  f"Delay={episode_delay:.4f}, "
                  f"Accuracy={avg_accuracy:.3f}, "
                  f"Battery={avg_battery:.3f}")

        # 保存模型
        if (episode + 1) % save_frequency == 0:
            model_path = os.path.join(self.save_dir, f"maddpg_episode_{episode + 1}.pth")
            self.maddpg.save_models(model_path)
            print(f"在episode {episode + 1}保存模型")

        # 评估性能
        if (episode + 1) % eval_frequency == 0:
            self._evaluate_current_policy(episode + 1)

    # 保存最终模型和统计数据
    final_model_path = os.path.join(self.save_dir, "maddpg_final.pth")
    self.maddpg.save_models(final_model_path)

    # 保存训练统计 (转换为JSON兼容格式)
    json_stats = {}
    for key, value in self.training_stats.items():
        if isinstance(value, list):
            json_stats[key] = [float(x) if hasattr(x, 'item') else x for x in value]
        else:
            json_stats[key] = float(value) if hasattr(value, 'item') else value

    stats_path = os.path.join(self.save_dir, "training_stats.json")
    with open(stats_path, 'w') as f:
        json.dump(json_stats, f, indent=2)

    print("训练完成!")
    return self.training_stats
```

**训练流程特点**:
- **Episode循环**: 1000个episode的训练
- **经验回放**: 每步存储经验到缓冲区
- **噪声衰减**: 探索噪声随训练进度衰减
- **定期保存**: 每100个episode保存模型
- **定期评估**: 每50个episode评估当前策略

### 8.3 策略评估方法
```python
def _evaluate_current_policy(self, episode: int, num_eval_episodes: int = 10):
    """评估当前策略 (无探索噪声)"""
    print(f"在episode {episode}评估策略...")

    eval_rewards = []
    eval_energies = []
    eval_delays = []

    for _ in range(num_eval_episodes):
        states = self.env.reset()
        episode_reward = 0.0
        episode_energy = 0.0
        episode_delay = 0.0
        step_count = 0

        while step_count < self.env.max_steps:
            # 获取动作 (无噪声)
            actions = self.maddpg.act(states, add_noise=False)
            next_states, rewards, dones, info = self.env.step(actions)

            episode_reward += sum(rewards)
            episode_energy += sum(info['execution_results']['device_energies'])
            episode_delay += sum(info['execution_results']['device_delays'])

            step_count += 1
            states = next_states

            if all(dones):
                break

        eval_rewards.append(episode_reward)
        eval_energies.append(episode_energy)
        eval_delays.append(episode_delay)

    print(f"评估结果 - 奖励: {np.mean(eval_rewards):.2f} ± {np.std(eval_rewards):.2f}, "
          f"能耗: {np.mean(eval_energies):.2f} ± {np.std(eval_energies):.2f}, "
          f"延迟: {np.mean(eval_delays):.4f} ± {np.std(eval_delays):.4f}")
```

### 8.4 基线算法评估
```python
def evaluate_baselines(self, num_episodes: int = 100) -> Dict[str, Any]:
    """评估所有基线算法"""
    print("评估基线算法...")

    baseline_results = {}

    for name, baseline in self.baselines.items():
        print(f"评估 {name}...")
        results = evaluate_baseline(baseline, self.env, num_episodes)
        baseline_results[name] = results

        print(f"{name} - 奖励: {results['avg_reward']:.2f} ± {results['std_reward']:.2f}, "
              f"能耗: {results['avg_energy']:.2f} ± {results['std_energy']:.2f}, "
              f"延迟: {results['avg_delay']:.4f} ± {results['std_delay']:.4f}")

    # 保存基线结果
    baseline_path = os.path.join(self.save_dir, "baseline_results.json")
    with open(baseline_path, 'w') as f:
        json.dump(baseline_results, f, indent=2)

    return baseline_results
```

### 8.5 训练曲线可视化
```python
def plot_training_curves(self):
    """绘制训练曲线"""
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))

    # Episode奖励
    axes[0, 0].plot(self.training_stats['episode_rewards'])
    axes[0, 0].set_title('Episode Rewards')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Total Reward')

    # Episode能耗
    axes[0, 1].plot(self.training_stats['episode_energies'])
    axes[0, 1].set_title('Episode Energy Consumption')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Total Energy (J)')

    # Episode延迟
    axes[0, 2].plot(self.training_stats['episode_delays'])
    axes[0, 2].set_title('Episode Delays')
    axes[0, 2].set_xlabel('Episode')
    axes[0, 2].set_ylabel('Total Delay (s)')

    # Episode准确率
    axes[1, 0].plot(self.training_stats['episode_accuracies'])
    axes[1, 0].set_title('Episode Accuracies')
    axes[1, 0].set_xlabel('Episode')
    axes[1, 0].set_ylabel('Average Accuracy')

    # 电池水平
    axes[1, 1].plot(self.training_stats['avg_battery_levels'])
    axes[1, 1].set_title('Average Battery Levels')
    axes[1, 1].set_xlabel('Episode')
    axes[1, 1].set_ylabel('Battery Ratio')

    # 约束违反
    axes[1, 2].plot(self.training_stats['episode_violations'])
    axes[1, 2].set_title('Constraint Violations')
    axes[1, 2].set_xlabel('Episode')
    axes[1, 2].set_ylabel('Total Violations')

    plt.tight_layout()
    plt.savefig(os.path.join(self.save_dir, 'training_curves.png'),
                dpi=300, bbox_inches='tight')
    plt.show()

### 8.6 基线对比可视化
```python
def compare_with_baselines(self, baseline_results: Dict[str, Any]):
    """与基线算法对比"""
    # 准备对比数据
    algorithms = list(baseline_results.keys()) + ['MADDPG']

    # 计算MADDPG最终性能 (最后10%的episode)
    final_episodes = int(len(self.training_stats['episode_rewards']) * 0.1)
    maddpg_reward = np.mean(self.training_stats['episode_rewards'][-final_episodes:])
    maddpg_energy = np.mean(self.training_stats['episode_energies'][-final_episodes:])
    maddpg_delay = np.mean(self.training_stats['episode_delays'][-final_episodes:])
    maddpg_accuracy = np.mean(self.training_stats['episode_accuracies'][-final_episodes:])

    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))

    # 奖励对比
    rewards = [baseline_results[alg]['avg_reward'] for alg in baseline_results.keys()] + [maddpg_reward]
    axes[0, 0].bar(algorithms, rewards)
    axes[0, 0].set_title('Average Rewards Comparison')
    axes[0, 0].set_ylabel('Average Reward')
    axes[0, 0].tick_params(axis='x', rotation=45)

    # 能耗对比
    energies = [baseline_results[alg]['avg_energy'] for alg in baseline_results.keys()] + [maddpg_energy]
    axes[0, 1].bar(algorithms, energies)
    axes[0, 1].set_title('Average Energy Consumption Comparison')
    axes[0, 1].set_ylabel('Average Energy (J)')
    axes[0, 1].tick_params(axis='x', rotation=45)

    # 延迟对比
    delays = [baseline_results[alg]['avg_delay'] for alg in baseline_results.keys()] + [maddpg_delay]
    axes[1, 0].bar(algorithms, delays)
    axes[1, 0].set_title('Average Delay Comparison')
    axes[1, 0].set_ylabel('Average Delay (s)')
    axes[1, 0].tick_params(axis='x', rotation=45)

    # 准确率对比
    accuracies = [baseline_results[alg]['avg_accuracy'] for alg in baseline_results.keys()] + [maddpg_accuracy]
    axes[1, 1].bar(algorithms, accuracies)
    axes[1, 1].set_title('Average Accuracy Comparison')
    axes[1, 1].set_ylabel('Average Accuracy')
    axes[1, 1].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig(os.path.join(self.save_dir, 'baseline_comparison.png'),
                dpi=300, bbox_inches='tight')
    plt.show()
```

### 8.7 主函数和命令行接口
```python
def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MADDPG训练用于边缘计算优化')
    parser.add_argument('--episodes', type=int, default=1000, help='训练episode数量')
    parser.add_argument('--eval_episodes', type=int, default=100, help='评估episode数量')
    parser.add_argument('--save_dir', type=str, default='results', help='结果保存目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--eval_only', action='store_true', help='仅评估基线算法')

    args = parser.parse_args()

    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    # 创建环境
    env = EdgeComputingEnvironment(random_seed=args.seed)

    # 创建训练器
    trainer = MADDPGTrainer(env, args.save_dir)

    if args.eval_only:
        # 仅评估基线算法
        baseline_results = trainer.evaluate_baselines(args.eval_episodes)
        print("基线评估完成!")
    else:
        # 训练MADDPG
        training_stats = trainer.train(args.episodes)

        # 绘制训练曲线
        trainer.plot_training_curves()

        # 评估基线算法
        baseline_results = trainer.evaluate_baselines(args.eval_episodes)

        # 与基线对比
        trainer.compare_with_baselines(baseline_results)

        print(f"训练和评估完成! 结果保存在 {args.save_dir}")

if __name__ == "__main__":
    main()
```

**命令行使用示例**:
```bash
# 完整训练和评估
python main.py --episodes 1000 --save_dir results --seed 42

# 仅评估基线算法
python main.py --eval_only --eval_episodes 100 --save_dir results

# 自定义参数训练
python main.py --episodes 500 --eval_episodes 50 --save_dir custom_results --seed 123
```

## 9. 关键算法公式总结

### 9.1 能耗模型公式
```
计算能耗: E_comp = κ × f³ × T_local
通信能耗: E_comm = P_tx × D_tx / R_tx
静态能耗: E_static = P_static × T_total
总能耗: E_total = E_comp + E_comm + E_static
```

### 9.2 延迟模型公式
```
本地延迟: T_local = C_local / (f_device × g_device)
通信延迟: T_comm = D_tx / R_tx
边缘延迟: T_edge = C_edge / (f_server × g_server)
总延迟: T_total = T_local + T_comm + T_edge
```

### 9.3 自适应权重公式
```
权重函数: α_m = 1 / (1 + exp(-β(B_m/B_max - θ)))
目标函数: J_m = α_m × T_m + (1-α_m) × E_m
多设备目标: J = (1/M) × Σ_m [α_m × T_m + (1-α_m) × E_m]
```

### 9.4 MADDPG更新公式
```
Critic损失: L_critic = MSE(Q(s,a), r + γQ'(s',a'))
Actor损失: L_actor = -E[Q(s, μ(s))]
软更新: θ_target = τθ + (1-τ)θ_target
```

### 9.5 奖励函数公式
```
基础奖励: R_base = -(α × T_norm + (1-α) × E_norm)
约束惩罚: P = P_acc + P_delay + P_battery + P_server
最终奖励: R = R_base - P
```

## 10. 技术创新点总结

### 10.1 多智能体协作机制
- **分散执行集中训练**: MADDPG架构实现智能体间协作
- **异构设备建模**: 不同设备具有不同的电池、频率、网络条件
- **动态负载均衡**: 服务器容量约束和负载管理

### 10.2 电池感知优化
- **自适应权重机制**: 基于电池状态动态调整能耗-延迟权衡
- **个体化奖励设计**: 每个设备根据自身状态获得不同奖励
- **电池约束建模**: 电池耗尽作为硬约束处理

### 10.3 早期退出与分割计算
- **ResNet-50早期退出**: 5个退出点提供准确率-计算量权衡
- **动态分割决策**: 智能体学习最优分割点选择
- **准确率约束满足**: 自动选择满足要求的最小退出点

### 10.4 现实系统建模
- **真实硬件参数**: 基于NVIDIA Jetson Xavier和Laptop 4060规格
- **动态网络条件**: 15.5-25 Mbps的边缘网络带宽模拟
- **多约束优化**: 延迟、准确率、电池、服务器容量约束

这个框架为异构边缘计算环境下的DNN推理优化提供了完整的解决方案，通过多智能体强化学习实现了设备间的智能协作和资源优化配置。
```
```
```
"""
Multi-Agent Deep Deterministic Policy Gradient (MADDPG) Implementation
For Heterogeneous Multi-Edge Server DNN Inference Optimization
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import random
from collections import deque
from typing import List, Dict, Any, Tuple, Optional
from config import config

class Actor(nn.Module):
    """
    Actor network for MADDPG - decentralized execution
    Takes local state and outputs actions for one agent
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = None,
                 dropout_rate: float = 0.1):
        super(Actor, self).__init__()
        
        if hidden_dims is None:
            hidden_dims = config.maddpg.actor_hidden_dims
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # Build network layers
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, action_dim))
        layers.append(nn.Tanh())  # Actions in [-1, 1] range
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize network weights"""
        for layer in self.network:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                nn.init.constant_(layer.bias, 0.0)
    
    def forward(self, state):
        """Forward pass"""
        return self.network(state)

class Critic(nn.Module):
    """
    Critic network for MADDPG - centralized training
    Takes global state and all actions, outputs Q-value
    """
    
    def __init__(self, global_state_dim: int, global_action_dim: int, 
                 hidden_dims: List[int] = None, dropout_rate: float = 0.1):
        super(Critic, self).__init__()
        
        if hidden_dims is None:
            hidden_dims = config.maddpg.critic_hidden_dims
        
        self.global_state_dim = global_state_dim
        self.global_action_dim = global_action_dim
        
        # Build network layers
        layers = []
        prev_dim = global_state_dim + global_action_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # Output layer (single Q-value)
        layers.append(nn.Linear(prev_dim, 1))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize network weights"""
        for layer in self.network:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                nn.init.constant_(layer.bias, 0.0)
    
    def forward(self, global_state, global_actions):
        """Forward pass"""
        x = torch.cat([global_state, global_actions], dim=-1)
        return self.network(x)

class ReplayBuffer:
    """Experience replay buffer for MADDPG"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
    
    def push(self, experience: Tuple):
        """Add experience to buffer"""
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> List[Tuple]:
        """Sample batch of experiences"""
        return random.sample(self.buffer, batch_size)
    
    def __len__(self):
        return len(self.buffer)

class OUNoise:
    """Ornstein-Uhlenbeck noise for exploration"""
    
    def __init__(self, action_dim: int, mu: float = 0.0, theta: float = 0.15, 
                 sigma: float = 0.2):
        self.action_dim = action_dim
        self.mu = mu
        self.theta = theta
        self.sigma = sigma
        self.state = np.ones(action_dim) * mu
        
    def reset(self):
        """Reset noise state"""
        self.state = np.ones(self.action_dim) * self.mu
    
    def sample(self):
        """Sample noise"""
        dx = self.theta * (self.mu - self.state) + self.sigma * np.random.randn(self.action_dim)
        self.state += dx
        return self.state

class MADDPGAgent:
    """Single agent in MADDPG framework"""
    
    def __init__(self, agent_id: int, state_dim: int, action_dim: int, 
                 global_state_dim: int, global_action_dim: int):
        self.agent_id = agent_id
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.global_state_dim = global_state_dim
        self.global_action_dim = global_action_dim
        
        # Networks
        self.actor = Actor(state_dim, action_dim)
        self.critic = Critic(global_state_dim, global_action_dim)
        self.target_actor = Actor(state_dim, action_dim)
        self.target_critic = Critic(global_state_dim, global_action_dim)
        
        # Copy weights to target networks
        self.target_actor.load_state_dict(self.actor.state_dict())
        self.target_critic.load_state_dict(self.critic.state_dict())
        
        # Optimizers
        self.actor_optimizer = optim.Adam(self.actor.parameters(), 
                                        lr=config.maddpg.lr_actor)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), 
                                         lr=config.maddpg.lr_critic)
        
        # Noise for exploration
        self.noise = OUNoise(action_dim)
        
        # Training parameters
        self.gamma = config.maddpg.gamma
        self.tau = config.maddpg.tau
        
    def act(self, state: np.ndarray, add_noise: bool = True, 
            noise_scale: float = 1.0) -> np.ndarray:
        """Select action using actor network"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        self.actor.eval()
        with torch.no_grad():
            action = self.actor(state_tensor).cpu().numpy()[0]
        self.actor.train()
        
        if add_noise:
            noise = self.noise.sample() * noise_scale
            action += noise
            action = np.clip(action, -1.0, 1.0)
        
        return action
    
    def update_critic(self, experiences: List[Tuple], other_agents: List['MADDPGAgent']):
        """Update critic network"""
        states, actions, rewards, next_states, dones = zip(*experiences)

        # Convert to tensors - handle nested lists properly
        batch_size = len(states)
        num_agents = len(states[0])

        # Convert states (batch_size, num_agents, state_dim)
        states_array = np.array(states)
        states = torch.FloatTensor(states_array)

        # Convert actions (batch_size, num_agents, action_dim)
        actions_array = np.array(actions)
        actions = torch.FloatTensor(actions_array)

        # Convert rewards (batch_size, num_agents)
        rewards_array = np.array(rewards)
        rewards = torch.FloatTensor(rewards_array)

        # Convert next_states (batch_size, num_agents, state_dim)
        next_states_array = np.array(next_states)
        next_states = torch.FloatTensor(next_states_array)

        # Convert dones (batch_size, num_agents)
        dones_array = np.array(dones)
        dones = torch.FloatTensor(dones_array)

        # Get next actions from target actors
        next_actions = []
        for i, agent in enumerate(other_agents):
            if i == self.agent_id:
                next_action = self.target_actor(next_states[:, i])
            else:
                next_action = agent.target_actor(next_states[:, i])
            next_actions.append(next_action)

        next_actions = torch.cat(next_actions, dim=1)

        # Calculate target Q-values
        next_global_states = next_states.view(batch_size, -1)
        target_q = self.target_critic(next_global_states, next_actions)

        # Get reward and done for this agent
        agent_rewards = rewards[:, self.agent_id].unsqueeze(1)
        agent_dones = dones[:, self.agent_id].unsqueeze(1)

        target_q = agent_rewards + (self.gamma * target_q * (1 - agent_dones))

        # Calculate current Q-values
        global_states = states.view(batch_size, -1)
        global_actions = actions.view(batch_size, -1)
        current_q = self.critic(global_states, global_actions)

        # Critic loss
        critic_loss = F.mse_loss(current_q, target_q.detach())

        # Update critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic.parameters(),
                                     config.maddpg.grad_clip)
        self.critic_optimizer.step()

        return critic_loss.item()
    
    def update_actor(self, experiences: List[Tuple], other_agents: List['MADDPGAgent']):
        """Update actor network"""
        states, actions, _, _, _ = zip(*experiences)

        # Convert to tensors - handle nested lists properly
        batch_size = len(states)
        states_array = np.array(states)
        states = torch.FloatTensor(states_array)

        # Get actions from all actors
        all_actions = []
        for i, agent in enumerate(other_agents):
            if i == self.agent_id:
                action = self.actor(states[:, i])
            else:
                action = agent.actor(states[:, i]).detach()
            all_actions.append(action)

        all_actions = torch.cat(all_actions, dim=1)

        # Actor loss (negative Q-value)
        global_states = states.view(batch_size, -1)
        actor_loss = -self.critic(global_states, all_actions).mean()

        # Update actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(),
                                     config.maddpg.grad_clip)
        self.actor_optimizer.step()

        return actor_loss.item()
    
    def soft_update(self):
        """Soft update of target networks"""
        for target_param, param in zip(self.target_actor.parameters(), 
                                     self.actor.parameters()):
            target_param.data.copy_(self.tau * param.data + 
                                  (1.0 - self.tau) * target_param.data)
        
        for target_param, param in zip(self.target_critic.parameters(), 
                                     self.critic.parameters()):
            target_param.data.copy_(self.tau * param.data + 
                                  (1.0 - self.tau) * target_param.data)

class MADDPG:
    """Multi-Agent Deep Deterministic Policy Gradient"""
    
    def __init__(self, num_agents: int, state_dim: int, action_dim: int):
        self.num_agents = num_agents
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.global_state_dim = num_agents * state_dim
        self.global_action_dim = num_agents * action_dim
        
        # Create agents
        self.agents = [
            MADDPGAgent(i, state_dim, action_dim, 
                       self.global_state_dim, self.global_action_dim)
            for i in range(num_agents)
        ]
        
        # Replay buffer
        self.replay_buffer = ReplayBuffer(config.maddpg.buffer_size)
        
        # Training parameters
        self.batch_size = config.maddpg.batch_size
        self.update_frequency = config.maddpg.update_frequency
        self.warmup_steps = config.maddpg.warmup_steps
        
        # Noise scheduling
        self.initial_noise = config.maddpg.initial_noise
        self.final_noise = config.maddpg.final_noise
        self.noise_decay_episodes = config.maddpg.noise_decay_episodes
        self.current_noise_scale = self.initial_noise
        
        # Training statistics
        self.step_count = 0
        self.episode_count = 0
        self.training_stats = {
            'actor_losses': [[] for _ in range(num_agents)],
            'critic_losses': [[] for _ in range(num_agents)]
        }
    
    def act(self, states: List[np.ndarray], add_noise: bool = True) -> List[np.ndarray]:
        """Get actions for all agents"""
        actions = []
        for i, (agent, state) in enumerate(zip(self.agents, states)):
            action = agent.act(state, add_noise, self.current_noise_scale)
            actions.append(action)
        return actions
    
    def step(self, states: List[np.ndarray], actions: List[np.ndarray], 
             rewards: List[float], next_states: List[np.ndarray], 
             dones: List[bool]):
        """Store experience and update if ready"""
        # Store experience
        experience = (states, actions, rewards, next_states, dones)
        self.replay_buffer.push(experience)
        
        self.step_count += 1
        
        # Update if enough experiences and time to update
        if (len(self.replay_buffer) >= self.warmup_steps and 
            self.step_count % self.update_frequency == 0):
            self.update()
    
    def update(self):
        """Update all agents"""
        if len(self.replay_buffer) < self.batch_size:
            return
        
        # Sample experiences
        experiences = self.replay_buffer.sample(self.batch_size)
        
        # Update each agent
        for i, agent in enumerate(self.agents):
            # Update critic
            critic_loss = agent.update_critic(experiences, self.agents)
            self.training_stats['critic_losses'][i].append(critic_loss)
            
            # Update actor
            actor_loss = agent.update_actor(experiences, self.agents)
            self.training_stats['actor_losses'][i].append(actor_loss)
            
            # Soft update target networks
            agent.soft_update()
    
    def update_noise_scale(self, episode: int, max_episodes: int):
        """Update exploration noise scale"""
        decay_episodes = int(max_episodes * self.noise_decay_episodes)
        if episode < decay_episodes:
            progress = episode / decay_episodes
            self.current_noise_scale = (self.initial_noise * (1 - progress) + 
                                      self.final_noise * progress)
        else:
            self.current_noise_scale = self.final_noise
    
    def reset_noise(self):
        """Reset noise for all agents"""
        for agent in self.agents:
            agent.noise.reset()
    
    def save_models(self, filepath: str):
        """Save all agent models"""
        checkpoint = {
            'agents': [],
            'training_stats': self.training_stats,
            'step_count': self.step_count,
            'episode_count': self.episode_count
        }
        
        for i, agent in enumerate(self.agents):
            agent_data = {
                'actor_state_dict': agent.actor.state_dict(),
                'critic_state_dict': agent.critic.state_dict(),
                'target_actor_state_dict': agent.target_actor.state_dict(),
                'target_critic_state_dict': agent.target_critic.state_dict(),
                'actor_optimizer_state_dict': agent.actor_optimizer.state_dict(),
                'critic_optimizer_state_dict': agent.critic_optimizer.state_dict()
            }
            checkpoint['agents'].append(agent_data)
        
        torch.save(checkpoint, filepath)
    
    def load_models(self, filepath: str):
        """Load all agent models"""
        checkpoint = torch.load(filepath)
        
        for i, agent in enumerate(self.agents):
            agent_data = checkpoint['agents'][i]
            agent.actor.load_state_dict(agent_data['actor_state_dict'])
            agent.critic.load_state_dict(agent_data['critic_state_dict'])
            agent.target_actor.load_state_dict(agent_data['target_actor_state_dict'])
            agent.target_critic.load_state_dict(agent_data['target_critic_state_dict'])
            agent.actor_optimizer.load_state_dict(agent_data['actor_optimizer_state_dict'])
            agent.critic_optimizer.load_state_dict(agent_data['critic_optimizer_state_dict'])
        
        self.training_stats = checkpoint['training_stats']
        self.step_count = checkpoint['step_count']
        self.episode_count = checkpoint['episode_count']
